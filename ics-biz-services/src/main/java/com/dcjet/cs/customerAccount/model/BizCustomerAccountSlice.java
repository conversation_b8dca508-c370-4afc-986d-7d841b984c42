package com.dcjet.cs.customerAccount.model;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
/**
 * generated by Generate 神码
 * 
 *
 * <AUTHOR>
 * @date: 2025-7-28
 */
@Setter
@Getter
@Table(name = "T_BIZ_CUSTOMER_ACCOUNT_SLICE")
public class BizCustomerAccountSlice implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
     * 主键
     */
	 @Id
	@Column(name = "SID")
	private  String sid;
	/**
     * 创建人
     */
	@Column(name = "CREATE_BY")
	private  String createBy;
	/**
     * 创建时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "CREATE_TIME")
	private  Date createTime;
	/**
     * 创建人名称
     */
	@Column(name = "CREATE_USER_NAME")
	private  String createUserName;
	/**
     * 更新人
     */
	@Column(name = "UPDATE_BY")
	private  String updateBy;
	/**
     * 最后修改时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "UPDATE_TIME")
	private  Date updateTime;
	/**
     * 最后修改人名称
     */
	@Column(name = "UPDATE_USER_NAME")
	private  String updateUserName;
	/**
     * 企业编码
     */
	@Column(name = "TRADE_CODE")
	private  String tradeCode;
	/**
     * 创建人部门编码
     */
	@Column(name = "SYS_ORG_CODE")
	private  String sysOrgCode;
	/**
     * 拓展字段1
     */
	@Column(name = "EXTEND1")
	private  String extend1;
	/**
     * 拓展字段2
     */
	@Column(name = "EXTEND2")
	private  String extend2;
	/**
     * 拓展字段3
     */
	@Column(name = "EXTEND3")
	private  String extend3;
	/**
     * 拓展字段4
     */
	@Column(name = "EXTEND4")
	private  String extend4;
	/**
     * 拓展字段5
     */
	@Column(name = "EXTEND5")
	private  String extend5;
	/**
     * 拓展字段6
     */
	@Column(name = "EXTEND6")
	private  String extend6;
	/**
     * 拓展字段7
     */
	@Column(name = "EXTEND7")
	private  String extend7;
	/**
     * 拓展字段8
     */
	@Column(name = "EXTEND8")
	private  String extend8;
	/**
     * 拓展字段9
     */
	@Column(name = "EXTEND9")
	private  String extend9;
	/**
     * 拓展字段10
     */
	@Column(name = "EXTEND10")
	private  String extend10;
	/**
     * 业务类型
     */
	@Column(name = "BUSINESS_TYPE")
	private  String businessType;
	/**
     * 结算单号
     */
	@Column(name = "ACCOUNT_NO")
	private  String accountNo;
	/**
     * 合同号
     */
	@Column(name = "CONTRACT_NO")
	private  String contractNo;
	/**
     * 出口币种
     */
	@Column(name = "CURR_E")
	private  String currE;
	/**
     * 进口币种
     */
	@Column(name = "CURR_I")
	private  String currI;
	/**
     * 出口汇率
     */
	@Column(name = "EXCHANGE_RATE_E")
	private  BigDecimal exchangeRateE;
	/**
     * 进口汇率
     */
	@Column(name = "EXCHANGE_RATE_I")
	private  BigDecimal exchangeRateI;
	/**
     * 出口货款
     */
	@Column(name = "GOODS_PRICE_E")
	private  BigDecimal goodsPriceE;
	/**
     * 进口货款
     */
	@Column(name = "GOODS_PRICE_I")
	private  BigDecimal goodsPriceI;
	/**
     * 中烟代理费率%
     */
	@Column(name = "AGENT_FEE_RATE")
	private  BigDecimal agentFeeRate;
	/**
     * 中烟代理费（不含税）
     */
	@Column(name = "AGENT_FEE")
	private  BigDecimal agentFee;
	/**
     * 中烟代理费税额
     */
	@Column(name = "AGENT_TAX_FEE")
	private  BigDecimal agentTaxFee;
	/**
     * 中烟代理费（价税合计）
     */
	@Column(name = "AGENT_FEE_TOTAL")
	private  BigDecimal agentFeeTotal;
	/**
     * 结算日期
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "BUSINESS_DATE")
	private  Date businessDate;
	/**
     * 商品名称
     */
	@Column(name = "G_NAME")
	@JsonProperty("gName")
	private  String gName;
	/**
     * 发送财务系统
     */
	@Column(name = "SEND_FINANCE")
	private  String sendFinance;
	/**
     * 商品与数量
     */
	@Column(name = "PRODUCR_SOME")
	private  String producrSome;
	/**
     * 备注
     */
	@Column(name = "NOTE")
	private  String note;
	/**
     * 货款比例
     */
	@Column(name = "FREIGHT_RATIO")
	private  String freightRatio;
	/**
     * 是否红冲
     */
	@Column(name = "RED_FLUSH")
	private  String redFlush;
	/**
     * 单据状态
     */
	@Column(name = "STATUS")
	private  String status;
	/**
     * 审核状态
     */
	@Column(name = "APPR_STATUS")
	private  String apprStatus;
	/**
     * 确认时间
     */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	@Column(name = "CONFIRM_TIME")
	private  Date confirmTime;
	/**
     * 是否确认
     */
	@Column(name = "IS_CONFIRM")
	private  String isConfirm;
	/**
     * 外商合同、进货明细数据标记
     */
	@Column(name = "PURCHASE_MARK")
	private  String purchaseMark;
	/**
     * 外商合同、进货明细数据标记
     */
	@Column(name = "PURCHASE_NO_MARK")
	private  String purchaseNoMark;
	/**
     * 出口货款(人民币)
     */
	@Column(name = "GOODS_PRICE_E_RMB")
	private  BigDecimal goodsPriceERmb;
	/**
     * 进口货款(人民币)
     */
	@Column(name = "GOODS_PRICE_I_RMB")
	private  BigDecimal goodsPriceIRmb;
	/**
     * 增值税率%
     */
	@Column(name = "VAT_RATE")
	private  BigDecimal vatRate;
	/**
     * 货代费
     */
	@Column(name = "FREIGHT_FORWARDING_FEE")
	private  BigDecimal freightForwardingFee;
	/**
     * 保险费
     */
	@Column(name = "INSURANCE_FEE")
	private  BigDecimal insuranceFee;
	/**
     * 已结算款项合计
     */
	@Column(name = "COST_FEE")
	private  BigDecimal costFee;
	/**
     * 实际预收款
     */
	@Column(name = "DEPOSIT_RECEIVED")
	private  BigDecimal depositReceived;
	/**
     * 应退款项
     */
	@Column(name = "REFUND_FEE")
	private  BigDecimal refundFee;
	/**
     * 共计金额(人民币)
     */
	@Column(name = "TOTAL_AMOUNT")
	private  BigDecimal totalAmount;
	/**
     * 客户
     */
	@Column(name = "CUSTOMER")
	private  String customer;
	/**
     * 业务地点
     */
	@Column(name = "BUSINESS_LOCATION")
	private  String businessLocation;
	@Column(name = "PURCHASE_ORDER_NO")
	private  String purchaseOrderNo;

	@Transient
	private String insertTimeFrom;
	@Transient
	private String insertTimeTo;

	@Transient
	private String createrBy;

	@Transient
	private String createrUserName;

	@Transient
	private Date createrTime;
	@Transient
	private String agentFeeRateStr;
	@Transient
	private String goodsPriceIStr;
	@Transient
	private String goodsPriceEStr;
	@Transient
	private String goodsPriceIRmbStr;
	@Transient
	private String goodsPriceERmbStr;
	@Transient
	private String totalAmountRmbStr;
	@Transient
	private String exchangeRateIStr;
	@Transient
	private String exchangeRateEStr;
	@Transient
	private String businessDateStr;
}
