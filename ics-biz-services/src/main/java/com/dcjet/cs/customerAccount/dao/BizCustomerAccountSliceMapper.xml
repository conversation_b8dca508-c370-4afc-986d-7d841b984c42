<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.customerAccount.dao.BizCustomerAccountSliceMapper">
    <resultMap id="bizCustomerAccountSliceResultMap" type="com.dcjet.cs.customerAccount.model.BizCustomerAccountSlice">
		<id column="SID" property="sid" jdbcType="VARCHAR" />
		<result column="CREATE_BY" property="createBy" jdbcType="VARCHAR" />
		<result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" />
		<result column="CREATE_USER_NAME" property="createUserName" jdbcType="VARCHAR" />
		<result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR" />
		<result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR" />
		<result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR" />
		<result column="SYS_ORG_CODE" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="EXTEND1" property="extend1" jdbcType="VARCHAR" />
		<result column="EXTEND2" property="extend2" jdbcType="VARCHAR" />
		<result column="EXTEND3" property="extend3" jdbcType="VARCHAR" />
		<result column="EXTEND4" property="extend4" jdbcType="VARCHAR" />
		<result column="EXTEND5" property="extend5" jdbcType="VARCHAR" />
		<result column="EXTEND6" property="extend6" jdbcType="VARCHAR" />
		<result column="EXTEND7" property="extend7" jdbcType="VARCHAR" />
		<result column="EXTEND8" property="extend8" jdbcType="VARCHAR" />
		<result column="EXTEND9" property="extend9" jdbcType="VARCHAR" />
		<result column="EXTEND10" property="extend10" jdbcType="VARCHAR" />
		<result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR" />
		<result column="ACCOUNT_NO" property="accountNo" jdbcType="VARCHAR" />
		<result column="CONTRACT_NO" property="contractNo" jdbcType="VARCHAR" />
		<result column="CURR_E" property="currE" jdbcType="VARCHAR" />
		<result column="CURR_I" property="currI" jdbcType="VARCHAR" />
		<result column="EXCHANGE_RATE_E" property="exchangeRateE" jdbcType="NUMERIC" />
		<result column="EXCHANGE_RATE_I" property="exchangeRateI" jdbcType="NUMERIC" />
		<result column="GOODS_PRICE_E" property="goodsPriceE" jdbcType="NUMERIC" />
		<result column="GOODS_PRICE_I" property="goodsPriceI" jdbcType="NUMERIC" />
		<result column="AGENT_FEE_RATE" property="agentFeeRate" jdbcType="NUMERIC" />
		<result column="AGENT_FEE" property="agentFee" jdbcType="NUMERIC" />
		<result column="AGENT_TAX_FEE" property="agentTaxFee" jdbcType="NUMERIC" />
		<result column="AGENT_FEE_TOTAL" property="agentFeeTotal" jdbcType="NUMERIC" />
		<result column="BUSINESS_DATE" property="businessDate" jdbcType="TIMESTAMP" />
		<result column="G_NAME" property="gName" jdbcType="VARCHAR" />
		<result column="SEND_FINANCE" property="sendFinance" jdbcType="VARCHAR" />
		<result column="PRODUCR_SOME" property="producrSome" jdbcType="VARCHAR" />
		<result column="NOTE" property="note" jdbcType="VARCHAR" />
		<result column="FREIGHT_RATIO" property="freightRatio" jdbcType="VARCHAR" />
		<result column="RED_FLUSH" property="redFlush" jdbcType="VARCHAR" />
		<result column="STATUS" property="status" jdbcType="VARCHAR" />
		<result column="APPR_STATUS" property="apprStatus" jdbcType="VARCHAR" />
		<result column="CONFIRM_TIME" property="confirmTime" jdbcType="TIMESTAMP" />
		<result column="IS_CONFIRM" property="isConfirm" jdbcType="VARCHAR" />
		<result column="PURCHASE_MARK" property="purchaseMark" jdbcType="VARCHAR" />
		<result column="PURCHASE_NO_MARK" property="purchaseNoMark" jdbcType="VARCHAR" />
		<result column="GOODS_PRICE_E_RMB" property="goodsPriceERmb" jdbcType="NUMERIC" />
		<result column="GOODS_PRICE_I_RMB" property="goodsPriceIRmb" jdbcType="NUMERIC" />
		<result column="VAT_RATE" property="vatRate" jdbcType="NUMERIC" />
		<result column="FREIGHT_FORWARDING_FEE" property="freightForwardingFee" jdbcType="NUMERIC" />
		<result column="INSURANCE_FEE" property="insuranceFee" jdbcType="NUMERIC" />
		<result column="COST_FEE" property="costFee" jdbcType="NUMERIC" />
		<result column="DEPOSIT_RECEIVED" property="depositReceived" jdbcType="NUMERIC" />
		<result column="REFUND_FEE" property="refundFee" jdbcType="NUMERIC" />
		<result column="TOTAL_AMOUNT" property="totalAmount" jdbcType="NUMERIC" />
		<result column="CUSTOMER" property="customer" jdbcType="VARCHAR" />
		<result column="BUSINESS_LOCATION" property="businessLocation" jdbcType="VARCHAR" />
		<result column="PURCHASE_ORDER_NO" property="purchaseOrderNo" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     SID
     ,COALESCE(update_by,create_by) as createrBy
     ,COALESCE(update_user_name,create_user_name) as createrUserName
     ,COALESCE(update_time,create_time) as createrTime
     ,CREATE_BY
     ,CREATE_TIME
     ,CREATE_USER_NAME
     ,UPDATE_BY
     ,UPDATE_TIME
     ,UPDATE_USER_NAME
     ,TRADE_CODE
     ,SYS_ORG_CODE
     ,EXTEND1
     ,EXTEND2
     ,EXTEND3
     ,EXTEND4
     ,EXTEND5
     ,EXTEND6
     ,EXTEND7
     ,EXTEND8
     ,EXTEND9
     ,EXTEND10
     ,BUSINESS_TYPE
     ,ACCOUNT_NO
     ,CONTRACT_NO
     ,CURR_E
     ,CURR_I
     ,EXCHANGE_RATE_E
     ,EXCHANGE_RATE_I
     ,GOODS_PRICE_E
     ,GOODS_PRICE_I
     ,AGENT_FEE_RATE
     ,AGENT_FEE
     ,AGENT_TAX_FEE
     ,AGENT_FEE_TOTAL
     ,BUSINESS_DATE
     ,G_NAME
     ,SEND_FINANCE
     ,PRODUCR_SOME
     ,NOTE
     ,FREIGHT_RATIO
     ,RED_FLUSH
     ,STATUS
     ,APPR_STATUS
     ,CONFIRM_TIME
     ,IS_CONFIRM
     ,PURCHASE_MARK
     ,PURCHASE_NO_MARK
     ,GOODS_PRICE_E_RMB
     ,GOODS_PRICE_I_RMB
     ,VAT_RATE
     ,FREIGHT_FORWARDING_FEE
     ,INSURANCE_FEE
     ,COST_FEE
     ,DEPOSIT_RECEIVED
     ,REFUND_FEE
     ,TOTAL_AMOUNT
     ,CUSTOMER
     ,BUSINESS_LOCATION
     ,PURCHASE_ORDER_NO
    </sql>
    <sql id="condition">
    <if test="businessType != null and businessType != ''">
		and BUSINESS_TYPE = #{businessType}
	</if>
    <if test="accountNo != null and accountNo != ''">
	  and ACCOUNT_NO like '%'|| #{accountNo} || '%'
	</if>
    <if test="contractNo != null and contractNo != ''">
	  and CONTRACT_NO like '%'|| #{contractNo} || '%'
	</if>
    <if test="purchaseOrderNo != null and purchaseOrderNo != ''">
	  and PURCHASE_ORDER_NO like '%'|| #{purchaseOrderNo} || '%'
	</if>
    <if test="status != null and status != ''">
		and STATUS = #{status}
	</if>
    <if test="customer != null and customer != ''">
	  and CUSTOMER like '%'|| #{customer} || '%'
	</if>
        <if test="createBy != null and createBy != ''">
            and COALESCE(update_by,create_by) like '%'|| #{createBy} || '%'
        </if>
        <if test="tradeCode != null and tradeCode != ''">
            and trade_code = #{tradeCode}
        </if>
        <if test="insertTimeFrom != null and insertTimeFrom != ''">
            <![CDATA[ and coalesce(t.update_time,t.create_time) >= to_date(#{insertTimeFrom}, 'yyyy-MM-dd hh24:mi:ss')]]>
        </if>
        <if test="insertTimeTo != null and insertTimeTo != ''">
            <![CDATA[ and coalesce(t.update_time,t.create_time) < DATEADD(DAY, 1, to_date(#{insertTimeTo}, 'yyyy-mm-dd hh24:mi:ss')) ]]>
        </if>
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizCustomerAccountSliceResultMap" parameterType="com.dcjet.cs.customerAccount.model.BizCustomerAccountSlice">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_BIZ_CUSTOMER_ACCOUNT_SLICE t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <select id="checkIdNotCancel" resultType="java.lang.String">
        select sid
        from T_BIZ_CUSTOMER_ACCOUNT_SLICE t
        where t.account_no in  (select  distinct account_no
                                from T_BIZ_CUSTOMER_ACCOUNT_SLICE t
                                where SID = #{sid}
        ) and  t.STATUS !=2;
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BIZ_CUSTOMER_ACCOUNT_SLICE t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
    <select id="getSerialNo" resultType="java.lang.String">
        SELECT * FROM (
                          SELECT ACCOUNT_NO from T_BIZ_CUSTOMER_ACCOUNT_SUMMARY tbcas
                          UNION
                          SELECT ACCOUNT_NO from T_BIZ_CUSTOMER_ACCOUNT_TOBACOO tbcat
                          UNION
                          SELECT ACCOUNT_NO from T_BIZ_CUSTOMER_ACCOUNT_SLICE tbcasl) t
        WHERE t.ACCOUNT_NO like '%'|| #{prefix} || '%' ORDER BY t.ACCOUNT_NO DESC limit 1
    </select>
    <select id="getOrderSupplierList" resultType="java.util.Map">
        select
            distinct
            t.customer as "value",
            m.MERCHANT_NAME_CN as "label"
        from  T_BIZ_CUSTOMER_ACCOUNT_SLICE t LEFT JOIN T_BIZ_MERCHANT m ON t.customer = m.merchant_code
        where t.TRADE_CODE = #{tradeCode};
    </select>
    <select id="getCreateUserList" resultType="java.util.Map">
        select
            distinct
            COALESCE(update_by,create_by) as "value"
                   ,COALESCE(update_user_name,create_user_name) as "label"
        from  T_BIZ_CUSTOMER_ACCOUNT_SLICE t
        where t.TRADE_CODE = #{tradeCode};
    </select>
    <select id="getForeignContractHeadListQty" resultType="com.dcjet.cs.seven.model.SevenForeignContractHead">
        SELECT t.id,t.contract_no,t.SIGN_DATE,t.BUYER ,t.SELLER ,t.CURR,
               sum(CASE WHEN tl.BODY_TYPE = 'slice' THEN COALESCE(tl.MONEY_AMOUNT, 0) ELSE 0 END ) AS decTotalToListI,
               sum(CASE WHEN tl.BODY_TYPE = 'process' THEN COALESCE(tl.MONEY_AMOUNT, 0) ELSE 0 END ) AS decTotalToListE
        FROM T_BIZ_SEVEN_FOREIGN_CONTRACT_HEAD t
                 LEFT JOIN T_BIZ_SEVEN_FOREIGN_CONTRACT_LIST tl ON t.id = tl.head_id
        WHERE NOT EXISTS (SELECT 1 FROM T_BIZ_CUSTOMER_ACCOUNT_SLICE tc WHERE tc.CONTRACT_NO = t.contract_no AND tc.STATUS != '2' )
          AND t.DATA_STATUS = '1'
        GROUP BY t.id,t.contract_no,t.SIGN_DATE,t.BUYER ,t.SELLER ,t.CURR
    </select>
</mapper>
