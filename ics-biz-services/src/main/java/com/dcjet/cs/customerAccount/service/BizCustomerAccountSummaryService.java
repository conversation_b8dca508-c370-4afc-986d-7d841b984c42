package com.dcjet.cs.customerAccount.service;
import com.dcjet.cs.Utils.NumberFormatterUtils;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.bi.model.ExpenseIList;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.customerAccount.dao.BizCustomerAccountTobacooMapper;
import com.dcjet.cs.customerAccount.model.BizCustomerAccount;
import com.dcjet.cs.customerAccount.model.BizCustomerAccountTobacoo;
import com.dcjet.cs.dec.dao.BizSmokeCommonMapper;
import com.dcjet.cs.dec.dao.BizSmokeMachineIncomingGoodsHeadMapper;
import com.dcjet.cs.dec.model.BizSmokeGoodsExtractHeadList;
import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsHead;
import com.dcjet.cs.dec.model.BizSmokeMachineIncomingGoodsList;
import com.dcjet.cs.dto.equipment.ForeignContractHeadDto;
import com.dcjet.cs.dto.equipment.ForeignContractHeadParam;
import com.dcjet.cs.equipment.dao.ForeignContractHeadMapper;
import com.dcjet.cs.equipment.dao.ForeignContractListMapper;
import com.dcjet.cs.equipment.mapper.ForeignContractHeadDtoMapper;
import com.dcjet.cs.equipment.model.ForeignContractHead;
import com.dcjet.cs.equipment.model.ForeignContractList;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.customerAccount.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.customerAccount.dao.BizCustomerAccountSummaryMapper;
import com.dcjet.cs.customerAccount.mapper.BizCustomerAccountSummaryDtoMapper;
import com.dcjet.cs.customerAccount.model.BizCustomerAccountSummary;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.pcode.service.PCodeHolder;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-7-1
 */
@Service
public class BizCustomerAccountSummaryService extends BaseService<BizCustomerAccountSummary> {
    @Resource
    private BizCustomerAccountSummaryMapper bizCustomerAccountSummaryMapper;
    @Resource
    private BizCustomerAccountSummaryDtoMapper bizCustomerAccountSummaryDtoMapper;
    @Override
    public Mapper<BizCustomerAccountSummary> getMapper() {
        return bizCustomerAccountSummaryMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizCustomerAccountSummaryParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizCustomerAccountSummaryDto>> getListPaged(BizCustomerAccountSummaryParam bizCustomerAccountSummaryParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizCustomerAccountSummary bizCustomerAccountSummary = bizCustomerAccountSummaryDtoMapper.toPo(bizCustomerAccountSummaryParam);
        bizCustomerAccountSummary.setTradeCode(userInfo.getCompany());
        Page<BizCustomerAccountSummary> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizCustomerAccountSummaryMapper.getList(bizCustomerAccountSummary));
        List<BizCustomerAccountSummaryDto> bizCustomerAccountSummaryDtos = page.getResult().stream().map(head -> {
            BizCustomerAccountSummaryDto dto = bizCustomerAccountSummaryDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizCustomerAccountSummaryDto>> paged = ResultObject.createInstance(bizCustomerAccountSummaryDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizCustomerAccountSummaryParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizCustomerAccountSummaryDto insert(BizCustomerAccountSummaryParam bizCustomerAccountSummaryParam, UserInfoToken userInfo) {
        BizCustomerAccountSummary bizCustomerAccountSummary = bizCustomerAccountSummaryDtoMapper.toPo(bizCustomerAccountSummaryParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizCustomerAccountSummary.setSid(sid);
        bizCustomerAccountSummary.setTradeCode(userInfo.getCompany());
        bizCustomerAccountSummary.setCreateBy(userInfo.getUserNo());
        bizCustomerAccountSummary.setCreateTime(new Date());
        bizCustomerAccountSummary.setCreateUserName(userInfo.getUserName());
        bizCustomerAccountSummary.setBusinessType("3");
        bizCustomerAccountSummary.setApprStatus("0");
        bizCustomerAccountSummary.setStatus("0");
        bizCustomerAccountSummary.setSendFinance("0");
        bizCustomerAccountSummary.setBusinessDate(new Date());
        // 新增数据
        int insertStatus = bizCustomerAccountSummaryMapper.insert(bizCustomerAccountSummary);
        return  insertStatus > 0 ? bizCustomerAccountSummaryDtoMapper.toDto(bizCustomerAccountSummary) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizCustomerAccountSummaryParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizCustomerAccountSummaryDto update(BizCustomerAccountSummaryParam bizCustomerAccountSummaryParam, UserInfoToken userInfo) {
        BizCustomerAccountSummary bizCustomerAccountSummary = bizCustomerAccountSummaryMapper.selectByPrimaryKey(bizCustomerAccountSummaryParam.getSid());
        bizCustomerAccountSummaryDtoMapper.updatePo(bizCustomerAccountSummaryParam, bizCustomerAccountSummary);
        bizCustomerAccountSummary.setUpdateBy(userInfo.getUserNo());
        bizCustomerAccountSummary.setUpdateTime(new Date());
        bizCustomerAccountSummary.setUpdateUserName(userInfo.getUserName());
        // 更新数据
        int update = bizCustomerAccountSummaryMapper.updateByPrimaryKey(bizCustomerAccountSummary);
        return update > 0 ? bizCustomerAccountSummaryDtoMapper.toDto(bizCustomerAccountSummary) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizCustomerAccountSummaryMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizCustomerAccountSummaryDto> selectAll(BizCustomerAccountSummaryParam exportParam, UserInfoToken userInfo) {
        BizCustomerAccountSummary bizCustomerAccountSummary = bizCustomerAccountSummaryDtoMapper.toPo(exportParam);
         bizCustomerAccountSummary.setTradeCode(userInfo.getCompany());
        List<BizCustomerAccountSummaryDto> bizCustomerAccountSummaryDtos = new ArrayList<>();
        List<BizCustomerAccountSummary> bizCustomerAccountSummarys = bizCustomerAccountSummaryMapper.getList(bizCustomerAccountSummary);
        if (CollectionUtils.isNotEmpty(bizCustomerAccountSummarys)) {
            bizCustomerAccountSummaryDtos = bizCustomerAccountSummarys.stream().map(head -> {
                BizCustomerAccountSummaryDto dto = bizCustomerAccountSummaryDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizCustomerAccountSummaryDtos;
    }
    public ResultObject sendApproval(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("发送审批成功"));

        BizCustomerAccountSummary bizCustomerAccountSummary = bizCustomerAccountSummaryMapper.selectByPrimaryKey(sid);
        if (bizCustomerAccountSummary == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if (!CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizCustomerAccountSummary.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("请将进口计划操作确认再发送审批");
            return resultObject;
        }
        // 更新审批状态为2（审批中）
        bizCustomerAccountSummary.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVING.getValue());
        updateApprovalStatus(bizCustomerAccountSummary); // 调用更新审批状态的方法

        return resultObject;
    }
    @Transactional(rollbackFor = Exception.class)
    public void updateApprovalStatus(BizCustomerAccountSummary bizCustomerAccountSummary) {
        BizCustomerAccountSummary update = new BizCustomerAccountSummary();
        update.setSid(bizCustomerAccountSummary.getSid());
        update.setApprStatus(bizCustomerAccountSummary.getApprStatus());
        bizCustomerAccountSummaryMapper.updateByPrimaryKeySelective(update);
    }
    public ResultObject confirmStatus(BizCustomerAccountSummaryParam bizCustomerAccountSummaryParam, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("确认成功"));

        BizCustomerAccountSummary bizCustomerAccountSummary = bizCustomerAccountSummaryMapper.selectByPrimaryKey(bizCustomerAccountSummaryParam.getSid());
        if (bizCustomerAccountSummary == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizCustomerAccountSummary.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("该数据已经确认，无需重复操作");
            return resultObject;
        }
        BizCustomerAccountSummary update = bizCustomerAccountSummaryDtoMapper.toPo(bizCustomerAccountSummaryParam);
        update.setStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        update.setConfirmTime(new Date());
        update.setIsConfirm("1");
        bizCustomerAccountSummaryMapper.updateByPrimaryKeySelective(update);
        BizCustomerAccountSummaryDto dto = bizCustomerAccountSummaryDtoMapper.toDto(update);
        resultObject.setData(dto);
        return resultObject;
    }
    public ResultObject back(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("退单成功"));

        BizCustomerAccountSummary bizCustomerAccountSummary = bizCustomerAccountSummaryMapper.selectByPrimaryKey(sid);
        if (bizCustomerAccountSummary == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        // 检查审批状态
        if (CommonEnum.OrderApprStatusEnum.APPROVING.getValue().equals(bizCustomerAccountSummary.getApprStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("审批中的数据不允许退单");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.DRAFT.getValue().equals(bizCustomerAccountSummary.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("编制中的数据不允许退单");
            return resultObject;
        }
        BizCustomerAccountSummary update = new BizCustomerAccountSummary();
        update.setSid(sid);
        update.setStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        bizCustomerAccountSummaryMapper.updateByPrimaryKeySelective(update);
        return resultObject;
    }
    public ResultObject invalidate(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("作废成功"));

        BizCustomerAccountSummary bizCustomerAccountSummary = bizCustomerAccountSummaryMapper.selectByPrimaryKey(sid);
        if (bizCustomerAccountSummary == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        // 检查审批状态
        if (CommonEnum.OrderApprStatusEnum.APPROVING.getValue().equals(bizCustomerAccountSummary.getApprStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("审批中的数据不允许作废");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.CANCELLED.getValue().equals(bizCustomerAccountSummary.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("已作废的数据不允许作废");
            return resultObject;
        }
        BizCustomerAccountSummary update = new BizCustomerAccountSummary();
        update.setSid(sid);
        update.setStatus(CommonEnum.OrderStatusEnum.CANCELLED.getValue());
        bizCustomerAccountSummaryMapper.updateByPrimaryKeySelective(update);
        return resultObject;
    }

    public String getSerialNo(UserInfoToken userInfo){
        String head = "YJSKHJS";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        String date = sdf.format(new Date());
        String serialNo = "";
        String serialNo1 = bizCustomerAccountSummaryMapper.getSerialNo(head + date);
        if(StringUtils.isNotBlank(serialNo1)){
            serialNo = head + date + String.format("%03d",Integer.parseInt(serialNo1.substring(serialNo1.length() - 3)) + 1);
        }else {
            serialNo = head + date + "001";
        }
        return serialNo;
    }
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private ExportService exportService;
    @Resource
    private BizSmokeCommonMapper bizSmokeCommonMapper;
    private Map<String, String> createMerchantMap(List<BizMerchant> bizMerchants) {
        // 策略1：保留第一个出现的值（推荐）
        return bizMerchants.stream()
                .filter(merchant -> StringUtils.isNotBlank(merchant.getMerchantCode())
                        && StringUtils.isNotBlank(merchant.getMerchantNameCn()))
                .collect(Collectors.toMap(
                        BizMerchant::getMerchantCode,
                        BizMerchant::getMerchantNameCn,
                        (existing, replacement) -> existing, // 保留第一个出现的值
                        LinkedHashMap::new // 保持插入顺序
                ));
    }
    private String getMerchantNameSafely(Map<String, String> bizMerchantMap, String merchantCode) {
        if (StringUtils.isBlank(merchantCode)) {
            return "";
        }
        String merchantNameCn = bizMerchantMap.get(merchantCode);
        return StringUtils.isNotBlank(merchantNameCn) ? merchantNameCn : "";
    }
    @Resource
    private PCodeHolder pCodeHolder;
    public ResponseEntity printContact(BizCustomerAccountSummaryParam param, UserInfoToken userInfo) throws Exception{
        BizCustomerAccountSummary bizCustomerAccountSummary = bizCustomerAccountSummaryMapper.selectByPrimaryKey(param.getSid());
        SimpleDateFormat bus = new SimpleDateFormat("yyyy.MM.dd");

        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
        // 优化：支持过滤重复key，当存在重复的merchantCode时，保留第一个出现的merchantNameCn
        Map<String, String> bizMerchantMap = createMerchantMap(bizMerchants);
        ForeignContractHead foreignContractHead = foreignContractHeadMapper.selectByPrimaryKey(bizCustomerAccountSummary.getPurchaseNoMark());
        String tempName = "";

        if(bizCustomerAccountSummary == null || ObjectUtils.isEmpty(foreignContractHead)){
            throw new ErrorException(400,"中烟结算单不存在！");
        }else {
//            convertHeadToPrint(bizStoreIHead,bizMerchantMap);
            bizCustomerAccountSummary.setCustomerStr("该合同现已执行完毕，根据用户"+getMerchantNameSafely(bizMerchantMap,bizCustomerAccountSummary.getCustomer())
                    +"的要求，请及时结算并提供相应结算单据的复印件。多谢配合！");
            if(!ObjectUtils.isEmpty(foreignContractHead)){
                bizCustomerAccountSummary.setPriceTerms(foreignContractHead.getPriceTerm());
            }
            List<ForeignContractList> listByHeadId = foreignContractListMapper.getListByHeadId(bizCustomerAccountSummary.getPurchaseNoMark(),userInfo.getCompany());
            List<Map<String, String>> unitList = bizSmokeCommonMapper.getListCommonKeyValueList(userInfo.getCompany(), "UNIT");
            String s = "";
            String s2 = "";
            BigDecimal bigDecimal = new BigDecimal(0);
            BigDecimal bigDecimal2 = new BigDecimal(0);
            if(CollectionUtils.isNotEmpty(listByHeadId)){
                s = listByHeadId.get(0).getGName();
                for(Map<String, String> unit : unitList){
                    if(unit.get("value").equals(listByHeadId.get(0).getUnit())){
                        s2 = (unit.get("label"));
                    }
                }
                bigDecimal = listByHeadId.stream().map(ForeignContractList::getMoneyAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
                bigDecimal2 = listByHeadId.stream().map(ForeignContractList::getQty).reduce(BigDecimal.ZERO,BigDecimal::add);
            }
            bizCustomerAccountSummary.setBusinessDateStr(bus.format(bizCustomerAccountSummary.getBusinessDate()));
            bizCustomerAccountSummary.setProductName(s);
            bizCustomerAccountSummary.setGoodsPriceTotalStr(bizCustomerAccountSummary.getCurr()+ NumberFormatterUtils.formatNumber(bigDecimal));
            bizCustomerAccountSummary.setQtyStr(NumberFormatterUtils.formatNumber(bigDecimal2)+s2);

            if(StringUtils.equals(foreignContractHead.getBusinessPlace(),"1")){
                bizCustomerAccountSummary.setAgentFeeTotalStr("共计人民币"+NumberFormatterUtils.formatNumber(bizCustomerAccountSummary.getAgentFeeTotal())
                        +"元（1"+pCodeHolder.getValue(PCodeType.CURR, bizCustomerAccountSummary.getCurr())+"＝"+NumberFormatterUtils.formatNumber(bizCustomerAccountSummary.getExchangeRate())+"人民币）");
                bizCustomerAccountSummary.setAgentFeeStr(NumberFormatterUtils.formatNumber(bizCustomerAccountSummary.getAgentFee()));
                bizCustomerAccountSummary.setAgentTaxFeeStr(NumberFormatterUtils.formatNumber(bizCustomerAccountSummary.getAgentTaxFee()));
            }

//            bizCustomerAccountSummary.setAgentFeeRateStr(NumberFormatterUtils.formatNumber(bizCustomerAccountSummary.getAgentFeeRate()));
//            bizCustomerAccountSummary.setFreightForwardingFeeStr(NumberFormatterUtils.formatNumber(bizCustomerAccountSummary.getFreightForwardingFee()));
//            bizCustomerAccountSummary.setInsuranceFeeStr(NumberFormatterUtils.formatNumber(bizCustomerAccountSummary.getInsuranceFee()));
//            bizCustomerAccountSummary.setDepositReceivedStr(NumberFormatterUtils.formatNumber(bizCustomerAccountSummary.getDepositReceived()));
//            bizCustomerAccountSummary.setRefundFeeStr(NumberFormatterUtils.formatNumber(bizCustomerAccountSummary.getRefundFee()));
//            bizCustomerAccountSummary.setGoodsPriceRmbStr(NumberFormatterUtils.formatNumber(bizCustomerAccountSummary.getGoodsPriceRmb()));
//            bizCustomerAccountSummary.setAgentFeeTotalStr(NumberFormatterUtils.formatNumber(bizCustomerAccountSummary.getAgentFeeTotal()));
            bizCustomerAccountSummary.setCreaterUserName(StringUtils.isNotBlank(bizCustomerAccountSummary.getUpdateUserName()) ? bizCustomerAccountSummary.getUpdateUserName() : bizCustomerAccountSummary.getCreateUserName());
        }
        if(StringUtils.equals(foreignContractHead.getBusinessPlace(),"1")){
            tempName = "biz_customer_account_summary_link_1.xlsx";
        }else {
            tempName = "biz_customer_account_summary_link_0.xlsx";
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String formattedDate = sdf.format(new Date());

        String outName = xdoi18n.XdoI18nUtil.t("联系单")+formattedDate + param.getFileType();
        String fileName = UUID.randomUUID() +  ".xlsx";

        String exportFileName = exportService.export(Arrays.asList(bizCustomerAccountSummary),fileName, tempName);
        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));
        if (".pdf".equalsIgnoreCase(param.getFileType())) {
            fileBytes = ExportService.excelToPdf(fileBytes);
            fileBytes = ExportService.pdfSetTitle(fileBytes, outName);
        }
        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }
    public ResponseEntity printSummary(BizCustomerAccountSummaryParam param, UserInfoToken userInfo) throws Exception{
        BizCustomerAccountSummary bizCustomerAccountSummary = bizCustomerAccountSummaryMapper.selectByPrimaryKey(param.getSid());
        SimpleDateFormat bus = new SimpleDateFormat("yyyy.MM.dd");

        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
        // 优化：支持过滤重复key，当存在重复的merchantCode时，保留第一个出现的merchantNameCn
        Map<String, String> bizMerchantMap = createMerchantMap(bizMerchants);

        if(bizCustomerAccountSummary == null){
            throw new ErrorException(400,"中烟结算单不存在！");
        }else {
//            convertHeadToPrint(bizStoreIHead,bizMerchantMap);
            bizCustomerAccountSummary.setCustomer(getMerchantNameSafely(bizMerchantMap,bizCustomerAccountSummary.getCustomer()));
            ForeignContractHead foreignContractHead = foreignContractHeadMapper.selectByPrimaryKey(bizCustomerAccountSummary.getPurchaseNoMark());
            if(!ObjectUtils.isEmpty(foreignContractHead)){
                bizCustomerAccountSummary.setPriceTerms(foreignContractHead.getPriceTerm());
            }
            List<ForeignContractList> listByHeadId = foreignContractListMapper.getListByHeadId(bizCustomerAccountSummary.getPurchaseNoMark(),userInfo.getCompany());
            String s = "";
            BigDecimal bigDecimal = new BigDecimal(0);
            if(CollectionUtils.isNotEmpty(listByHeadId)){
                s = listByHeadId.get(0).getGName();
                bigDecimal = listByHeadId.stream().map(ForeignContractList::getMoneyAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
            }
            bizCustomerAccountSummary.setBusinessDateStr(bus.format(bizCustomerAccountSummary.getBusinessDate()));
            bizCustomerAccountSummary.setProductName(s);
            bizCustomerAccountSummary.setGoodsPriceTotalStr(bizCustomerAccountSummary.getCurr()+ NumberFormatterUtils.formatNumber(bigDecimal));
            bizCustomerAccountSummary.setAgentFeeRateStr(NumberFormatterUtils.formatNumber(bizCustomerAccountSummary.getAgentFeeRate()));
            bizCustomerAccountSummary.setFreightForwardingFeeStr(NumberFormatterUtils.formatNumber(bizCustomerAccountSummary.getFreightForwardingFee()));
            bizCustomerAccountSummary.setInsuranceFeeStr(NumberFormatterUtils.formatNumber(bizCustomerAccountSummary.getInsuranceFee()));
            bizCustomerAccountSummary.setDepositReceivedStr(NumberFormatterUtils.formatNumber(bizCustomerAccountSummary.getDepositReceived()));
            bizCustomerAccountSummary.setRefundFeeStr(NumberFormatterUtils.formatNumber(bizCustomerAccountSummary.getRefundFee()));
            bizCustomerAccountSummary.setGoodsPriceRmbStr(NumberFormatterUtils.formatNumber(bizCustomerAccountSummary.getGoodsPriceRmb()));
            bizCustomerAccountSummary.setAgentFeeTotalStr(NumberFormatterUtils.formatNumber(bizCustomerAccountSummary.getAgentFeeTotal()));
            bizCustomerAccountSummary.setCreaterUserName(StringUtils.isNotBlank(bizCustomerAccountSummary.getUpdateUserName()) ? bizCustomerAccountSummary.getUpdateUserName() : bizCustomerAccountSummary.getCreateUserName());
        }

        String tempName = "biz_customer_account_summary.xlsx";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String formattedDate = sdf.format(new Date());

        String outName = xdoi18n.XdoI18nUtil.t("总结算单")+formattedDate + param.getFileType();
        String fileName = UUID.randomUUID() +  ".xlsx";

        String exportFileName = exportService.export(Arrays.asList(bizCustomerAccountSummary),fileName, tempName);
        HttpHeaders h = new HttpHeaders();
        byte[] fileBytes = FileUtils.readFileToByteArray(new File(exportFileName));
        if (".pdf".equalsIgnoreCase(param.getFileType())) {
            fileBytes = ExportService.excelToPdf(fileBytes);
            fileBytes = ExportService.pdfSetTitle(fileBytes, outName);
        }
        h.setContentDispositionFormData("attachment", URLEncoder.encode(outName, "UTF-8"));
        h.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return new ResponseEntity<byte[]>(fileBytes, h, HttpStatus.OK);
    }

    public ResultObject getSupplierList(BizCustomerAccountSummaryParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = bizCustomerAccountSummaryMapper.getOrderSupplierList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关供应商信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }
    public ResultObject getCreateUserList(BizCustomerAccountSummaryParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取成功！");
        // 获取当前企业的供应商信息
        List<Map<String,String>>  list  = bizCustomerAccountSummaryMapper.getCreateUserList(userInfo.getCompany());
        if (CollectionUtils.isEmpty(list)){
            resultObject.setMessage("未查询到相关制单人信息");
            return resultObject;
        }
        resultObject.setData(list);
        return resultObject;
    }

    @Resource
    private ForeignContractHeadDtoMapper foreignContractHeadDtoMapper;
    @Resource
    private ForeignContractHeadMapper foreignContractHeadMapper;
    @Resource
    private BizCustomerAccountTobacooMapper bizCustomerAccountTobacooMapper;

    public ResultObject<List<ForeignContractHeadDto>> getForeignContractHeadList(ForeignContractHeadParam foreignContractHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        ForeignContractHead foreignContractHead = foreignContractHeadDtoMapper.toPo(foreignContractHeadParam);
        foreignContractHead.setTradeCode(userInfo.getCompany());
        Page<ForeignContractHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizCustomerAccountTobacooMapper.getForeignContractHeadList(foreignContractHead));
        List<ForeignContractHeadDto> dtos = page.getResult().stream().map(head -> {
            ForeignContractHeadDto dto = foreignContractHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
        ResultObject<List<ForeignContractHeadDto>> paged = ResultObject.createInstance(dtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    @Resource
    private ForeignContractListMapper foreignContractListMapper;
    @Resource
    private BizSmokeMachineIncomingGoodsHeadMapper bizSmokeMachineIncomingGoodsHeadMapper;
    public ResultObject insertByContract(BizCustomerAccountSummaryParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("新增成功"));
        ForeignContractHead foreignContractHead = foreignContractHeadMapper.selectByPrimaryKey(param.getSid());
        if(ObjectUtils.isEmpty(foreignContractHead)){
            resultObject.setSuccess(false);
            resultObject.setMessage("外商合同数据不存在无法生成客户结算数据");
            return resultObject;
        }
        List<ForeignContractList> listByHeadId = foreignContractListMapper.getListByHeadId(param.getSid(), userInfo.getCompany());
        List<BizCustomerAccountTobacoo>  tobacooList = bizCustomerAccountTobacooMapper.selectByPurchaseNoMark(param.getSid());
        List<BizSmokeMachineIncomingGoodsHead> listByHtId = bizSmokeMachineIncomingGoodsHeadMapper.getListByHtId(param.getSid());

        String sid = UUID.randomUUID().toString();
        BizCustomerAccountSummary insert = new BizCustomerAccountSummary();
        insert.setContractNo(foreignContractHead.getContractNo());
        insert.setAccountNo(getSerialNo(userInfo));
        insert.setCustomer(foreignContractHead.getBuyer());
        BigDecimal planPayNotify = bizCustomerAccountSummaryMapper.getPlanPayNotify(param.getSid());
        if(null != planPayNotify){
            insert.setDepositReceived(planPayNotify.setScale(2,RoundingMode.HALF_UP));
        }else {
            insert.setDepositReceived(new BigDecimal(0));
        }
        if(CollectionUtils.isNotEmpty(listByHtId)){
            String s = listByHtId.stream().map(BizSmokeMachineIncomingGoodsHead::getPurchaseNo).collect(Collectors.joining(";"));
            List<ExpenseIList> costIList = bizCustomerAccountSummaryMapper.getCostIList(s);
            BigDecimal tariffPrice = costIList.stream().filter(x -> x.getCostName().contains("货代费")).map(ExpenseIList::getExpenseAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            insert.setFreightForwardingFee(tariffPrice);
            BigDecimal vatPrice = costIList.stream().filter(x -> x.getCostName().contains("保险费")).map(ExpenseIList::getExpenseAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            insert.setInsuranceFee(vatPrice);
        }
        if(CollectionUtils.isNotEmpty(tobacooList)){
            insert.setCurr(tobacooList.get(0).getCurr());
            insert.setExchangeRate(tobacooList.get(0).getExchangeRate());
            insert.setGoodsPriceRmb(tobacooList.stream().map(BizCustomerAccountTobacoo::getGoodsPriceRmb).reduce(BigDecimal.ZERO,BigDecimal::add));
            insert.setAgentFeeRate(tobacooList.get(0).getAgentFeeRate());
//            insert.setVatRate(tobacooList.get(0).getVatRate());
            insert.setAgentFee(tobacooList.stream().map(BizCustomerAccountTobacoo::getTotalAgentFee).reduce(BigDecimal.ZERO,BigDecimal::add));
            insert.setAgentTaxFee(tobacooList.stream().map(BizCustomerAccountTobacoo::getTotalAgentTaxFee).reduce(BigDecimal.ZERO,BigDecimal::add));
            insert.setAgentFeeTotal(tobacooList.stream().map(BizCustomerAccountTobacoo::getTotalAgentFeeTotal).reduce(BigDecimal.ZERO,BigDecimal::add));
            if(null != insert.getFreightForwardingFee() && null != insert.getInsuranceFee()){
                insert.setCostFee(insert.getGoodsPriceRmb().add(insert.getAgentFeeTotal()).add(insert.getFreightForwardingFee()).add(insert.getInsuranceFee()).setScale(2,RoundingMode.HALF_UP));
                if(null != insert.getDepositReceived()){
                    insert.setRefundFee(insert.getDepositReceived().subtract(insert.getCostFee()).setScale(2,RoundingMode.HALF_UP));
                }
            }
        }
        //货代费 保险费 实际预收款 待定
//        insert.setFreightForwardingFee();
//        insert.setInsuranceFee();
//        insert.setDepositReceived();

        if(CollectionUtils.isNotEmpty(listByHeadId)){
            insert.setGoodsPrice(listByHeadId.stream().map(ForeignContractList::getMoneyAmount).reduce(BigDecimal.ZERO,BigDecimal::add));
            String gNames = listByHeadId.stream().map(ForeignContractList::getGName).collect(Collectors.joining(","));
            insert.setProducrSome(gNames);
//            BigDecimal reduce = listByHeadId.stream().map(contract -> contract.getQty().multiply(contract.getUnitPrice())).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
//            insert.setGoodsPrice(reduce);
//            if(null != insert.getExchangeRate()){
//                insert.setGoodsPriceRmb(insert.getGoodsPrice().multiply(insert.getExchangeRate()).setScale(2,RoundingMode.HALF_UP));
//                if(null != insert.getAgentFeeRate()){
//                    insert.setAgentFee(insert.getGoodsPriceRmb().multiply(insert.getAgentFeeRate()).divide(new BigDecimal(100),2,RoundingMode.HALF_UP));
//                    insert.setAgentTaxFee(insert.getAgentFee().multiply(insert.getVatRate()).divide(new BigDecimal(100),2,RoundingMode.HALF_UP));
//                    insert.setAgentFeeTotal(insert.getAgentFee().add(insert.getAgentTaxFee()));
//                }
//            }
        }
        insert.setBusinessDate(new Date());

        insert.setSid(sid);
        insert.setPurchaseMark("1");
        insert.setPurchaseNoMark(foreignContractHead.getId());
        insert.setStatus("0");
        insert.setRedFlush("1");
        insert.setSendFinance("0");
        insert.setTradeCode(userInfo.getCompany());
        insert.setCreateBy(userInfo.getUserNo());
        insert.setCreateTime(new Date());
        insert.setCreateUserName(userInfo.getUserName());
        insert.setBusinessType("3");
        insert.setApprStatus("0");

        int insertStatus = bizCustomerAccountSummaryMapper.insert(insert);
        insert.setCreaterBy(userInfo.getUserNo());
        insert.setCreaterTime(new Date());
        insert.setCreaterUserName(userInfo.getUserName());
        resultObject.setData(insertStatus > 0 ? bizCustomerAccountSummaryDtoMapper.toDto(insert) : null);
        return resultObject;
    }
}
