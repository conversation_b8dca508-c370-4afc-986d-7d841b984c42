package com.dcjet.cs.equipment.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
@Table(name = "T_BIZ_I_MACHINE_EQUIP_FOREIGN_CONTRACT_HEAD")
public class ForeignContractHead implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID")
    private String id;

    /**
     * 上一版本id
     */
    @Column(name = "PREV_VERSION_ID")
    private String prevVersionId;

    /**
     * 业务类型
     */
    @Column(name = "BUSINESS_TYPE")
    private String businessType;

    /**
     * 合同号
     */
    @Column(name = "CONTRACT_NO")
    private String contractNo;

    /**
     * 业务地点
     */
    @Column(name = "BUSINESS_PLACE")
    private String businessPlace;

    /**
     * 买方
     */
    @Column(name = "BUYER")
    private String buyer;

    /**
     * 卖方
     */
    @Column(name = "SELLER")
    private String seller;

    /**
     * 使用厂家
     */
    @Column(name = "USING_MANUFACTURER")
    private String usingManufacturer;

    /**
     * 国内委托方
     */
    @Column(name = "DOMESTIC_CLIENT")
    private String domesticClient;

    /**
     * 签约日期
     */
    @Column(name = "SIGN_DATE")
    private Date signDate;

    /**
     * 签约地点(中文)
     */
    @Column(name = "SIGN_PLACE_CN")
    private String signPlaceCn;

    /**
     * 签约地点(英文)
     */
    @Column(name = "SIGN_PLACE_EN")
    private String signPlaceEn;

    /**
     * 合同生效期
     */
    @Column(name = "CONTRACT_EFFECTIVE_DATE")
    private Date contractEffectiveDate;

    /**
     * 合同有效期
     */
    @Column(name = "CONTRACT_VALIDITY_DATE")
    private Date contractValidityDate;

    /**
     * 运输方式 0海运 1空运 2陆运
     */
    @Column(name = "TRANSPORT_MODE")
    private String transportMode;

    /**
     * 装运港
     */
    @Column(name = "SHIPPING_PORT")
    private String shippingPort;

    /**
     * 目的港
     */
    @Column(name = "DEST_PORT")
    private String destPort;

    /**
     * 报关口岸
     */
    @Column(name = "CUSTOMS_DECLARATION_PORT")
    private String customsDeclarationPort;

    /**
     * 付款方式 0付款交单 1即期信用证 2电汇 3预付款
     */
    @Column(name = "PAYMENT_METHOD")
    private String paymentMethod;

    /**
     * 币种
     */
    @Column(name = "CURR")
    private String curr;

    /**
     * 价格条款
     */
    @Column(name = "PRICE_TERM")
    private String priceTerm;

    /**
     * 价格条款对应港口 0起运港 1目的港
     */
    @Column(name = "PRICE_TERM_PORT")
    private String priceTermPort;

    /**
     * 建议授权签约人
     */
    @Column(name = "SUGGEST_AUTHOR_SIGNATORY")
    private String suggestAuthorSignatory;

    /**
     * 短溢数%
     */
    @Column(name = "SHORT_OVERFLOW_NUMBER")
    private BigDecimal shortOverflowNumber;

    /**
     * 备注
     */
    @Column(name = "NOTE")
    private String note;

    /**
     * 版本号
     */
    @Column(name = "VERSION_NO")
    private String versionNo;

    /**
     * 制单人
     */
    @Column(name = "DOCUMENT_MAKER")
    private String documentMaker;

    /**
     * 制单人编号
     */
    @Column(name = "DOCUMENT_MAKER_NO")
    private String documentMakerNo;

    /**
     * 制单日期
     */
    @Column(name = "DOCUMENT_MAKE_DATE")
    private Date documentMakeDate;

    /**
     * 单据状态 0编制 1确认 2作废
     */
    @Column(name = "DATA_STATUS")
    private String dataStatus;

    /**
     * 确认时间
     */
    @Column(name = "CONFIRM_TIME")
    private Date confirmTime;

    /**
     * 审批状态 0不涉及审批 1未审批 2审批中 3审批通过 4审批退回
     */
    @Column(name = "APPR_STATUS")
    private String apprStatus;

    /**
     * 创建人部门编码
     */
    @Column(name = "SYS_ORG_CODE")
    private String sysOrgCode;

    /**
     * 企业编码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;

    /**
     * 制单用户
     */
    @Column(name = "CREATE_BY")
    private String createBy;

    /**
     * 制单时间
     */
    @Column(name = "CREATE_TIME")
    private Date createTime;

    /**
     * 修改用户
     */
    @Column(name = "UPDATE_BY")
    private String updateBy;

    /**
     * 修改时间
     */
    @Column(name = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 制单用户名称
     */
    @Column(name = "CREATE_BY_NAME")
    private String createByName;

    /**
     * 修改用户名称
     */
    @Column(name = "UPDATE_BY_NAME")
    private String updateByName;

    /**
     * 扩展字段1
     */
    @Column(name = "EXTEND1")
    private String extend1;

    /**
     * 扩展字段2
     */
    @Column(name = "EXTEND2")
    private String extend2;
    /**
     * 扩展字段3
     */
    @Column(name = "EXTEND3")
    private String extend3;

    /**
     * 扩展字段4
     */
    @Column(name = "EXTEND4")
    private String extend4;

    /**
     * 扩展字段5
     */
    @Column(name = "EXTEND5")
    private String extend5;

    /**
     * 扩展字段6
     */
    @Column(name = "EXTEND6")
    private String extend6;

    /**
     * 扩展字段7
     */
    @Column(name = "EXTEND7")
    private String extend7;

    /**
     * 扩展字段8
     */
    @Column(name = "EXTEND8")
    private String extend8;

    /**
     * 扩展字段9
     */
    @Column(name = "EXTEND9")
    private String extend9;

    /**
     * 扩展字段10
     */
    @Column(name = "EXTEND10")
    private String extend10;


    /**
     * 制单时间开始
     */
    @Transient
    private String documentMakeDateFrom;

    /**
     * 制单时间结束
     */
    @Transient
    private String documentMakeDateTo;

    @Transient
    private BigDecimal amount;

    /**
     * 总金额
     */
    @Transient
    private BigDecimal totalMoney;


    /**
     * 总数量
     */
    @Transient
    private BigDecimal totalQty;
}