package com.dcjet.cs.equipment.service;
import com.dcjet.cs.Utils.NumberFormatterUtils;
import com.dcjet.cs.aeo.model.AeoAuditInfo;
import com.dcjet.cs.aeo.service.AeoAuditInfoService;
import com.dcjet.cs.approvalFlow.service.ApprovalFlowService;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.bi.service.BizMerchantService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.model.WorkFlowParam;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.dto.aeo.ApprovalFlowParam;
import com.dcjet.cs.dto.iEBusiness.ForContractListDto;
import com.dcjet.cs.equipment.dao.BizIEquipmentAgentAgreementListMapper;
import com.dcjet.cs.equipment.dao.ForeignContractHeadMapper;
import com.dcjet.cs.equipment.model.BizIEquipmentAgentAgreementList;
import com.dcjet.cs.equipment.model.ForeignContractHead;
import com.dcjet.cs.importedCigarettes.dao.BizIContractHeadMapper;
import com.dcjet.cs.params.dao.CityMapper;
import com.dcjet.cs.params.dao.EnterpriseRateMapper;
import com.dcjet.cs.params.dao.TransCodeMapper;
import com.dcjet.cs.params.model.City;
import com.dcjet.cs.params.model.EnterpriseRate;
import com.dcjet.cs.params.model.TransCode;
import com.xdo.pcode.service.PCodeHolder;
import com.yuncheng.workflow.api.WorkFlowBatchApi;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.equipment.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.common.exception.ErrorException;
import com.yuncheng.workflow.model.vo.NextNodeInfoBatchVo;
import com.yuncheng.workflow.model.vo.NextNodeInfoVo;
import com.yuncheng.workflow.vo.HttpResult;
import org.apache.commons.lang3.StringUtils;
import xdoi18n.XdoI18nUtil;
import com.dcjet.cs.equipment.dao.BizIEquipmentAgentAgreementMapper;
import com.dcjet.cs.equipment.mapper.BizIEquipmentAgentAgreementDtoMapper;
import com.dcjet.cs.equipment.model.BizIEquipmentAgentAgreement;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-7-2
 */
@Service
public class BizIEquipmentAgentAgreementService extends BaseService<BizIEquipmentAgentAgreement> implements ApprovalFlowService {
    @Resource
    private BizIEquipmentAgentAgreementMapper bizIEquipmentAgentAgreementMapper;
    @Resource
    private BizIEquipmentAgentAgreementListMapper bizIEquipmentAgentAgreementListMapper;
    @Resource
    private ExportService exportService;
    @Resource
    private BizMerchantService bizMerchantService;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private EnterpriseRateMapper enterpriseRateMapper;
    @Resource
    private PCodeHolder pcodeHolder;
    @Resource
    private CityMapper cityMapper;
    @Resource
    private BizIEquipmentAgentAgreementDtoMapper bizIEquipmentAgentAgreementDtoMapper;
    @Resource
    private CommonService commonService;
    @Resource
    private AeoAuditInfoService aeoAuditInfoService;
    @Resource
    private ForeignContractHeadMapper foreignContractHeadMapper;
    @Resource
    private BizIContractHeadMapper bizIContractHeadMapper;
    @Resource
    private TransCodeMapper transCodeMapper;
    @Override
    public Mapper<BizIEquipmentAgentAgreement> getMapper() {
        return bizIEquipmentAgentAgreementMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizIEquipmentAgentAgreementParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizIEquipmentAgentAgreementDto>> getListPaged(BizIEquipmentAgentAgreementParam bizIEquipmentAgentAgreementParam, PageParam pageParam) {
        // 启用分页查询
        BizIEquipmentAgentAgreement bizIEquipmentAgentAgreement = bizIEquipmentAgentAgreementDtoMapper.toPo(bizIEquipmentAgentAgreementParam);
        Page<BizIEquipmentAgentAgreement> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIEquipmentAgentAgreementMapper.getList(bizIEquipmentAgentAgreement));
        List<BizIEquipmentAgentAgreementDto> bizIEquipmentAgentAgreementDtos = page.getResult().stream().map(head -> {
            BizIEquipmentAgentAgreementDto dto = bizIEquipmentAgentAgreementDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizIEquipmentAgentAgreementDto>> paged = ResultObject.createInstance(bizIEquipmentAgentAgreementDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 待审核页面查询
     * @param bizIEquipmentAgentAgreementParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    public ResultObject<List<BizIEquipmentAgentAgreementDto>> getAeoListPaged(BizIEquipmentAgentAgreementParam bizIEquipmentAgentAgreementParam, PageParam pageParam, UserInfoToken userInfo) {
        // 初始化 WorkflowBatchApi Service
        WorkFlowBatchApi workFlowBatchApi = commonService.buildWorkFlowBatchApi(userInfo);
        HttpResult result = workFlowBatchApi.queryApprovalList(bizIEquipmentAgentAgreementParam.getBusinessType());
        List<String> ids = (List<String>) result.getResult();

        // 如果ids为空，直接返回空分页结果
        if (ids == null || ids.isEmpty()) {
            return ResultObject.createInstance(Collections.emptyList(), 0, pageParam.getPage());
        }

        bizIEquipmentAgentAgreementParam.setIds(ids);

        // 启用分页查询
        BizIEquipmentAgentAgreement bizIEquipmentAgentAgreement = bizIEquipmentAgentAgreementDtoMapper.toPo(bizIEquipmentAgentAgreementParam);
        bizIEquipmentAgentAgreement.setTradeCode(userInfo.getCompany());
        Page<BizIEquipmentAgentAgreement> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIEquipmentAgentAgreementMapper.getAeoList(bizIEquipmentAgentAgreement));
        // 将PO转为DTO返回给前端
        List<BizIEquipmentAgentAgreementDto> bizIEquipmentAgentAgreementDtoList = page.getResult().stream()
                .map(bizIEquipmentAgentAgreementDtoMapper::toDto)
                .collect(Collectors.toList());

        return ResultObject.createInstance(bizIEquipmentAgentAgreementDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 功能描述:新增
     *
     * @param bizIEquipmentAgentAgreementParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIEquipmentAgentAgreementDto insert(BizIEquipmentAgentAgreementParam bizIEquipmentAgentAgreementParam, UserInfoToken userInfo) {
        BizIEquipmentAgentAgreement bizIEquipmentAgentAgreement = bizIEquipmentAgentAgreementDtoMapper.toPo(bizIEquipmentAgentAgreementParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizIEquipmentAgentAgreement.setId(sid);
        bizIEquipmentAgentAgreement.setCreateBy(userInfo.getUserNo());
        bizIEquipmentAgentAgreement.setMakeBy(userInfo.getUserNo());
        bizIEquipmentAgentAgreement.setCreateTime(new Date());
        bizIEquipmentAgentAgreement.setMakeDate(new Date());
        bizIEquipmentAgentAgreement.setBillStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        bizIEquipmentAgentAgreement.setBusinessType(CommonEnum.businessTypeEnum.type_3.getCode());
        bizIEquipmentAgentAgreement.setApprovalStatus(CommonEnum.APPR_STAUTS_ENUM.APPR_STAUTS_0.getCode());
        bizIEquipmentAgentAgreement.setTradeCode(userInfo.getCompany());
        bizIEquipmentAgentAgreement.setParentId(bizIEquipmentAgentAgreementParam.getId());

        bizIEquipmentAgentAgreementMapper.insertListByForContract(bizIEquipmentAgentAgreementParam.getId(), sid, userInfo.getUserNo());
        BigDecimal total = bizIEquipmentAgentAgreementMapper.getContractAmount(sid);

        ForeignContractHead foreignContractHead = foreignContractHeadMapper.selectByPrimaryKey(bizIEquipmentAgentAgreementParam.getId());
        bizIEquipmentAgentAgreement.setContractNo(foreignContractHead.getContractNo());
//        bizIEquipmentAgentAgreement.setExtend9(foreignContractHead.getBuyer());
//        bizIEquipmentAgentAgreement.setExtend10(foreignContractHead.getSeller());
        bizIEquipmentAgentAgreement.setSignPlace(foreignContractHead.getSignPlaceCn());
        bizIEquipmentAgentAgreement.setCurrency(foreignContractHead.getCurr());
        bizIEquipmentAgentAgreement.setContractAmount(total);

        //新增审核记录
        AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
        aeoAuditInfo.setBusinessSid(bizIEquipmentAgentAgreement.getId());
        aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), "0", userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());

        // 新增数据
        int insertStatus = bizIEquipmentAgentAgreementMapper.insert(bizIEquipmentAgentAgreement);
        return  insertStatus > 0 ? bizIEquipmentAgentAgreementDtoMapper.toDto(bizIEquipmentAgentAgreement) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizIEquipmentAgentAgreementParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIEquipmentAgentAgreementDto update(BizIEquipmentAgentAgreementParam bizIEquipmentAgentAgreementParam, UserInfoToken userInfo) {
        BizIEquipmentAgentAgreement bizIEquipmentAgentAgreement = bizIEquipmentAgentAgreementMapper.selectByPrimaryKey(bizIEquipmentAgentAgreementParam.getSid());
        bizIEquipmentAgentAgreementDtoMapper.updatePo(bizIEquipmentAgentAgreementParam, bizIEquipmentAgentAgreement);
        bizIEquipmentAgentAgreement.setUpdateBy(userInfo.getUserNo());
        bizIEquipmentAgentAgreement.setUpdateTime(new Date());
        //业务类型+协议编号，唯一性校验
        if (bizIEquipmentAgentAgreementMapper.checkAgreementNo(bizIEquipmentAgentAgreementParam.getAgreementNo(),bizIEquipmentAgentAgreementParam.getId()) > 0){
            throw new ErrorException(400, "协议编号已存在，请重新输入！");
        }
        BigDecimal total = bizIEquipmentAgentAgreementMapper.getContractAmount(bizIEquipmentAgentAgreement.getId());
        bizIEquipmentAgentAgreement.setContractAmount(total);

        if (bizIEquipmentAgentAgreement.getAgencyRate() != null) {
            bizIEquipmentAgentAgreement.setAgencyFee(total.multiply(bizIEquipmentAgentAgreement.getAgencyRate()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        }
        // 更新数据
        int update = bizIEquipmentAgentAgreementMapper.updateByPrimaryKey(bizIEquipmentAgentAgreement);
        return update > 0 ? bizIEquipmentAgentAgreementDtoMapper.toDto(bizIEquipmentAgentAgreement) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        //校验编制状态
        if (bizIEquipmentAgentAgreementMapper.checkCanDelBySids(sids) > 0){
            throw new ErrorException(400, "仅编制状态数据允许删除");
        }
		bizIEquipmentAgentAgreementMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIEquipmentAgentAgreementDto> selectAll(BizIEquipmentAgentAgreementParam exportParam, UserInfoToken userInfo) {
        BizIEquipmentAgentAgreement bizIEquipmentAgentAgreement = bizIEquipmentAgentAgreementDtoMapper.toPo(exportParam);
        // bizIEquipmentAgentAgreement.setTradeCode(userInfo.getCompany());
        List<BizIEquipmentAgentAgreementDto> bizIEquipmentAgentAgreementDtos = new ArrayList<>();
        List<BizIEquipmentAgentAgreement> bizIEquipmentAgentAgreements = bizIEquipmentAgentAgreementMapper.getList(bizIEquipmentAgentAgreement);
        if (CollectionUtils.isNotEmpty(bizIEquipmentAgentAgreements)) {
            bizIEquipmentAgentAgreementDtos = bizIEquipmentAgentAgreements.stream().map(head -> {
                BizIEquipmentAgentAgreementDto dto = bizIEquipmentAgentAgreementDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIEquipmentAgentAgreementDtos;
    }

    /**
     * 确认数据状态
     * @param bizIEquipmentAgentAgreementParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject confirmDataStatus(BizIEquipmentAgentAgreementParam bizIEquipmentAgentAgreementParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, XdoI18nUtil.t("确认成功"));

        List<String> sids = bizIEquipmentAgentAgreementParam.getIds();
        if (CollectionUtils.isEmpty(sids)) {
            throw new ErrorException(400, "请选择要确认的数据！");
        }

        for (String sid : sids) {
            BizIEquipmentAgentAgreement bizIEquipmentAgentAgreement = bizIEquipmentAgentAgreementMapper.selectByPrimaryKey(sid);
            if (bizIEquipmentAgentAgreement == null) {
                throw new ErrorException(400, "设备代理协议数据不存在，请刷新");
            }

            if (CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizIEquipmentAgentAgreement.getDataState())) {
                throw new ErrorException(400, "该数据已经确认，无需重复操作");
            }

            // 更新数据状态为确认
            bizIEquipmentAgentAgreement.setDataState(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
            bizIEquipmentAgentAgreement.setBillStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
            bizIEquipmentAgentAgreement.setConfirmTime(new Date());
            bizIEquipmentAgentAgreement.setUpdateBy(userInfo.getUserNo());
            bizIEquipmentAgentAgreement.setUpdateTime(new Date());
            bizIEquipmentAgentAgreement.setUpdateUserName(userInfo.getUserName());

            int updateResult = bizIEquipmentAgentAgreementMapper.updateByPrimaryKey(bizIEquipmentAgentAgreement);
            if (updateResult <= 0) {
                throw new ErrorException(400, "确认失败，请重试");
            }
        }

        return result;
    }

    @Override
    public void startFlowBatch(NextNodeInfoBatchVo batchVo, ApprovalFlowParam approvalFlowParam, UserInfoToken userInfo) {
        //更新表头状态
        BizIEquipmentAgentAgreement bizIEquipmentAgentAgreement = bizIEquipmentAgentAgreementMapper.selectByPrimaryKey(approvalFlowParam.getIds().get(0));
        if (!CommonEnum.OrderApprStatusEnum.NOT_APPROVED.getValue().equals(bizIEquipmentAgentAgreement.getApprovalStatus())
                && !CommonEnum.OrderApprStatusEnum.APPROVAL_REJECTED.getValue().equals(bizIEquipmentAgentAgreement.getApprovalStatus())){
            throw new ErrorException(400, "只有未审核/审核退回数据允许操作发送审批");
        }
        bizIEquipmentAgentAgreement.setApprovalStatus(CommonEnum.OrderApprStatusEnum.APPROVING.getValue());
        bizIEquipmentAgentAgreement.setUpdateBy(userInfo.getUserNo());
        bizIEquipmentAgentAgreement.setUpdateTime(new Date());
        //记录flowInstanceId
        bizIEquipmentAgentAgreement.setExtend2(batchVo.getFlowInstanceId().get(0).get("flowInstanceId"));
        bizIEquipmentAgentAgreementMapper.updateByPrimaryKey(bizIEquipmentAgentAgreement);

        //新增审核记录
        AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
        aeoAuditInfo.setBusinessSid(bizIEquipmentAgentAgreement.getId());
        aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), "发送审核", userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
    }

    @Override
    public void audit(List<NextNodeInfoVo> nextNodeInfoVos, ApprovalFlowParam approvalFlowParam, Map<String, String> flowInstanceMap, UserInfoToken userInfo) {
        for (NextNodeInfoVo nextNodeInfoVo : nextNodeInfoVos) {
            BizIEquipmentAgentAgreement bizIEquipmentAgentAgreement = bizIEquipmentAgentAgreementMapper.selectByPrimaryKey(flowInstanceMap.get(nextNodeInfoVo.getFlowInstanceId()));
            if (nextNodeInfoVo.isFinish()) {
                bizIEquipmentAgentAgreement.setApprovalStatus(CommonEnum.OrderApprStatusEnum.APPROVED.getValue());
                bizIEquipmentAgentAgreement.setUpdateBy(userInfo.getUserNo());
                bizIEquipmentAgentAgreement.setUpdateTime(new Date());
                bizIEquipmentAgentAgreementMapper.updateByPrimaryKey(bizIEquipmentAgentAgreement);
                nextNodeInfoVo.setNodeName("审核通过");
            }
            //新增审核记录
            AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
            aeoAuditInfo.setBusinessSid(bizIEquipmentAgentAgreement.getId());
            aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), nextNodeInfoVo.getNodeName(), userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
        }
    }

    @Override
    public void reject(List<NextNodeInfoVo> nextNodeInfoVos, ApprovalFlowParam approvalFlowParam, UserInfoToken userInfo) {
        for (NextNodeInfoVo nextNodeInfoVo : nextNodeInfoVos) {
            //仅单条退回
            //回退到发起人
            BizIEquipmentAgentAgreement bizIEquipmentAgentAgreement = bizIEquipmentAgentAgreementMapper.selectByPrimaryKey(approvalFlowParam.getIds().get(0));

            bizIEquipmentAgentAgreement.setApprovalStatus(CommonEnum.OrderApprStatusEnum.APPROVAL_REJECTED.getValue());
            bizIEquipmentAgentAgreement.setUpdateBy(userInfo.getUserNo());
            bizIEquipmentAgentAgreement.setUpdateTime(new Date());
            bizIEquipmentAgentAgreementMapper.updateByPrimaryKey(bizIEquipmentAgentAgreement);

            //新增审核记录
            AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
            aeoAuditInfo.setBusinessSid(bizIEquipmentAgentAgreement.getId());
            aeoAuditInfo.setApprNote(approvalFlowParam.getApprMessage());
            aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), "审核退回", userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
        }
    }

    @Override
    public List<WorkFlowParam> getFlowList(List<String> ids) {
        return bizIEquipmentAgentAgreementMapper.getFlowList(ids);
    }

    public List<ForContractListDto> getForContractList(BizIEquipmentAgentAgreementParam param, UserInfoToken userInfo) {
        return bizIEquipmentAgentAgreementMapper.getForContractList(userInfo.getCompany(), param.getContractNo());
    }

    public BizIEquipmentAgentAgreementDto getDataByType(BizIEquipmentAgentAgreementParam param, UserInfoToken userInfo) {
        BizIEquipmentAgentAgreement bizIEquipmentAgentAgreement = bizIEquipmentAgentAgreementMapper.selectByPrimaryKey(param.getId());
        ForeignContractHead foreignContractHead = foreignContractHeadMapper.selectByPrimaryKey(bizIEquipmentAgentAgreement.getParentId());
        if (foreignContractHead == null) {
            throw new ErrorException(400, "外商合同数据不存在，无法带值");
        }
        //获取代理费率
        TransCode transCode = new TransCode();
        transCode.setBizType(bizIEquipmentAgentAgreement.getBusinessType());
        transCode.setTradeCode(userInfo.getCompany());
        List<TransCode> transCodeList = transCodeMapper.select(transCode);
        BigDecimal ieAgentFeeRate = BigDecimal.ZERO;
        BigDecimal hqAgentFeeRate = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(transCodeList)) {
            ieAgentFeeRate = transCodeList.get(0).getIeAgentFeeRate();
            hqAgentFeeRate = transCodeList.get(0).getHqAgentFeeRate();
        }
        //协议类型是0时，客户默认进出口公司；
        //协议类型是1时，客户默认买方；
        String supplierCode;
        if ("0".equals(param.getAgreementType())) {
            //获取中国烟草国际有限公司 code
            supplierCode = bizIContractHeadMapper.getBuyerCodeByName("中国烟草国际有限公司", userInfo.getCompany());
            String customerCode = bizIContractHeadMapper.getBuyerCodeByName("中国烟草上海进出口有限责任公司", userInfo.getCompany());
            bizIEquipmentAgentAgreement.setCustomer(customerCode);
            bizIEquipmentAgentAgreement.setAgencyRate(ieAgentFeeRate);
        } else {
            //进出口公司 code
            supplierCode = bizIContractHeadMapper.getBuyerCodeByName("中国烟草上海进出口有限责任公司", userInfo.getCompany());
            bizIEquipmentAgentAgreement.setCustomer(foreignContractHead.getBuyer());
            bizIEquipmentAgentAgreement.setAgencyRate(ieAgentFeeRate.add(hqAgentFeeRate));
        }
        bizIEquipmentAgentAgreement.setSupplier(supplierCode);

        BigDecimal total = bizIEquipmentAgentAgreementMapper.getContractAmount(bizIEquipmentAgentAgreement.getId());
        bizIEquipmentAgentAgreement.setContractAmount(total);

        bizIEquipmentAgentAgreement.setAgencyFee(total.multiply(bizIEquipmentAgentAgreement.getAgencyRate()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));

        return bizIEquipmentAgentAgreementDtoMapper.toDto(bizIEquipmentAgentAgreement);
    }

    public String printSign(BizIEquipmentAgentAgreementParam param, UserInfoToken userInfo) {
        String templateName = "3-代理协议会签单.xlsx";
        String fileName = UUID.randomUUID().toString() + ".xlsx";

        if (StringUtils.isEmpty(param.getId())){
            throw new ErrorException(400, "代理协议表头不能为空");
        }
        BizIEquipmentAgentAgreement bizIEquipmentAgentAgreement = bizIEquipmentAgentAgreementMapper.selectByPrimaryKey(param.getId());
        if (bizIEquipmentAgentAgreement == null) {
            throw new ErrorException(400, "代理协议数据不存在");
        }

        List<BizIEquipmentAgentAgreement> heads = new ArrayList<>();
        List<BizIEquipmentAgentAgreementList> lists = new ArrayList<>();

        Map<String, String> merchantMap = bizMerchantService.getMerchantMap(userInfo);
        lists = bizIEquipmentAgentAgreementListMapper.getListByHeadId(bizIEquipmentAgentAgreement.getId());

        if (CollectionUtils.isEmpty(lists)) {
            throw new ErrorException(400, "表体数据不能为空");
        }
        BigDecimal totalQty = lists.stream().filter(Objects::nonNull).map(BizIEquipmentAgentAgreementList::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
        String goodsName = lists.get(0).getProductName() + NumberFormatterUtils.formatNumber(totalQty) + "套";

        String seller = bizIEquipmentAgentAgreementMapper.getSellerByParentId(bizIEquipmentAgentAgreement.getParentId());

        seller = merchantMap.get(seller);

        String remarks = "";
        //与客户
        if ("customer".equals(param.getAgreementType())){
            bizIEquipmentAgentAgreement.setCustomer(merchantMap.get(bizIEquipmentAgentAgreement.getCustomer()));
            remarks = "我司代理 "+ bizIEquipmentAgentAgreement.getSupplier();
        }else{
            bizIEquipmentAgentAgreement.setCustomer(merchantMap.get(bizIEquipmentAgentAgreement.getSupplier()));
            remarks = "我司委托" + bizIEquipmentAgentAgreement.getCustomer();
        }
        remarks = remarks + "向" + seller +"购买" + goodsName + "。合同总价为"
                + bizIEquipmentAgentAgreement.getCurrency() + NumberFormatterUtils.formatNumber(bizIEquipmentAgentAgreement.getContractAmount())
                + ".-。 产品的技术规格、付款方式以及交货期均经过合同双方确认，中国烟草国际有限公司收"
                + bizIEquipmentAgentAgreement.getAgencyRate() + "%的代理费。";
        bizIEquipmentAgentAgreement.setExtend1(remarks);

        bizIEquipmentAgentAgreement.setContractAmountUpStr("(大写)：" + commonService.getCurrCn(bizIEquipmentAgentAgreement.getCurrency(), PCodeType.CURR_ALL) + NumberFormatterUtils.convertToChineseAmount(bizIEquipmentAgentAgreement.getContractAmount()));
        bizIEquipmentAgentAgreement.setContractAmountStr("(小写)：" + bizIEquipmentAgentAgreement.getCurrency() + NumberFormatterUtils.formatNumber(bizIEquipmentAgentAgreement.getContractAmount()));
        bizIEquipmentAgentAgreement.setCreateTime(new Date());

        heads.add(bizIEquipmentAgentAgreement);
        String exportFileName = exportService.export(heads,lists, fileName, templateName);

        return exportFileName;
    }

    public String print(BizIEquipmentAgentAgreementParam param, UserInfoToken userInfo) {
        String templateName = "3-代理协议打印.xlsx";
        String fileName = UUID.randomUUID().toString() + ".xlsx";

        List<BizIEquipmentAgentAgreement> heads = new ArrayList<>();
        List<BizIEquipmentAgentAgreementList> lists = new ArrayList<>();

        if (StringUtils.isEmpty(param.getId())){
            throw new ErrorException(400, "代理协议表头不能为空");
        }
        BizIEquipmentAgentAgreement bizIEquipmentAgentAgreement = bizIEquipmentAgentAgreementMapper.selectByPrimaryKey(param.getId());
        if (bizIEquipmentAgentAgreement == null) {
            throw new ErrorException(400, "代理协议数据不存在");
        }

        lists = bizIEquipmentAgentAgreementListMapper.getListByHeadId(bizIEquipmentAgentAgreement.getId());
        if (CollectionUtils.isEmpty(lists)) {
            throw new ErrorException(400, "表体数据不能为空");
        }

        BigDecimal totalQty = lists.stream().filter(Objects::nonNull).map(BizIEquipmentAgentAgreementList::getQuantity).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal rate = BigDecimal.ONE;
        if (!"CNY".equals(bizIEquipmentAgentAgreement.getCurrency())) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
            EnterpriseRate enterpriseRate = new EnterpriseRate();
            enterpriseRate.setTradeCode(userInfo.getCompany());
            enterpriseRate.setCurr(bizIEquipmentAgentAgreement.getCurrency());
            enterpriseRate.setMonth(sdf.format(bizIEquipmentAgentAgreement.getSignDate()));
            List<EnterpriseRate> select = enterpriseRateMapper.select(enterpriseRate);
            if (CollectionUtils.isNotEmpty(select)) {
                rate = select.get(0).getRate();
            }
        }

        //外商合同
        ForeignContractHead foreignContractHead = foreignContractHeadMapper.selectByPrimaryKey(bizIEquipmentAgentAgreement.getParentId());

        Map<String, String> merchantMap = bizMerchantService.getMerchantMap(userInfo);

        //与客户
        if ("customer".equals(param.getAgreementType())){
            if ("0".equals(bizIEquipmentAgentAgreement.getBusinessPlace())){
                templateName = "3-代理协议打印-上海.xlsx";
                bizIEquipmentAgentAgreement.setAgreementNo("协议号：" + bizIEquipmentAgentAgreement.getAgreementNo());
                String currCnName = commonService.getCurrCn(bizIEquipmentAgentAgreement.getCurrency(), PCodeType.CURR_ALL);
                bizIEquipmentAgentAgreement.setExtend1("2.1 合同金额：" + currCnName
                        + NumberFormatterUtils.formatNumber(bizIEquipmentAgentAgreement.getContractAmount().multiply(rate)) + "元；");
                bizIEquipmentAgentAgreement.setExtend2("2.2 合同费用组成：（本合同 "+ currCnName +"对人民币汇率暂以1" + currCnName +"= " + rate +"元人民币计算）；");
                bizIEquipmentAgentAgreement.setExtend3("2.2.1 进口设备价格"+ NumberFormatterUtils.formatNumber(bizIEquipmentAgentAgreement.getContractAmount()) +
                        foreignContractHead.getPriceTerm() + pcodeHolder.getValue(PCodeType.PORT_LIN, foreignContractHead.getShippingPort())
                        +"（进口合同号"
                        + bizIEquipmentAgentAgreement.getContractNo() +"）；");
                bizIEquipmentAgentAgreement.setExtend4("2.2.2 甲方以外商发票金额的2.5%向乙方支付代理费，即"
                        + NumberFormatterUtils.formatNumber(bizIEquipmentAgentAgreement.getContractAmount().multiply(bizIEquipmentAgentAgreement.getAgencyRate()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP))
                         +"；");
                bizIEquipmentAgentAgreement.setExtend5("2.2.3 以上两项费用约合人民币："
                        + NumberFormatterUtils.formatNumber(bizIEquipmentAgentAgreement.getContractAmount().multiply(rate)) +"。（除银行费用）均由甲方承担。代付费用预估人民币"+
                        NumberFormatterUtils.formatNumber(bizIEquipmentAgentAgreement.getContractAmount().multiply(rate).multiply(bizIEquipmentAgentAgreement.getAgencyRate().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)))
                        + "元, 代付费用包括：货代费用（国内外运输、报关费用、商检费用），国内段保险费用等。");
                bizIEquipmentAgentAgreement.setGoodsName(lists.get(0).getProductName());
            } else {
                templateName = "3-代理协议打印-外地.xlsx";
                bizIEquipmentAgentAgreement.setGoodsName("-代理服务("+ lists.get(0).getProductName() + ")");
                if (lists.size() > 1){
                    bizIEquipmentAgentAgreement.setExtend1("依据《中华人民共和国民法典》，根据国家局批准文号文和我方与中国烟草国际有限公司签订的合同（（" + lists.get(0).getProductName() +" 合同号：" +
                            bizIEquipmentAgentAgreement.getContractNo()+ "），现为购置烟草专用机械（引进）-代理服务("+ lists.get(0).getProductName() +")；为" +
                            NumberFormatterUtils.formatNumber(lists.get(0).getQuantity()) + " 台套" +
                            lists.get(0).getProductName() + "、" +NumberFormatterUtils.formatNumber(lists.get(1).getQuantity()) + "台套" +
                            lists.get(1).getProductName() + "、。。。对外商务联络，负责办理进口合同设备的全程运输、保险及清关手续,并负责送货至甲方指定地点。代理办理进口设备出关的事宜，并代为缴纳进口设备的关税和增值税，达成如下条款：");
                } else {
                    bizIEquipmentAgentAgreement.setExtend1("依据《中华人民共和国民法典》，根据国家局批准文号文和我方与中国烟草国际有限公司签订的合同（（" + lists.get(0).getProductName() +" 合同号：" +
                            bizIEquipmentAgentAgreement.getContractNo()+ "），现为购置烟草专用机械（引进）-代理服务("+ lists.get(0).getProductName() +")；为" +
                            NumberFormatterUtils.formatNumber(lists.get(0).getQuantity()) + " 台套" +
                            lists.get(0).getProductName() + "对外商务联络，负责办理进口合同设备的全程运输、保险及清关手续,并负责送货至甲方指定地点。代理办理进口设备出关的事宜，并代为缴纳进口设备的关税和增值税，达成如下条款：");
                }
                bizIEquipmentAgentAgreement.setExtend2(bizIEquipmentAgentAgreement.getContractNo() + "(" + lists.get(0).getProductName() +")");
                bizIEquipmentAgentAgreement.setContractAmountStr(NumberFormatterUtils.formatNumber(bizIEquipmentAgentAgreement.getContractAmount()) + " " + bizIEquipmentAgentAgreement.getCurrency());
                bizIEquipmentAgentAgreement.setExtend3(pcodeHolder.getValue(PCodeType.PORT_LIN, foreignContractHead.getShippingPort()));
                bizIEquipmentAgentAgreement.setExtend4("5.进口关税、增值税税单的抬头为：中国烟草国际有限公司（" + merchantMap.get(bizIEquipmentAgentAgreement.getCustomer())+"）。");
            }
        } else {
            templateName = "3-代理协议打印-中烟.xlsx";
            bizIEquipmentAgentAgreement.setExtend1("1.3质量标准：见烟草国营贸易货物进口专用合同"+ bizIEquipmentAgentAgreement.getContractNo() +"附件技术协议");
            bizIEquipmentAgentAgreement.setExtend2(pcodeHolder.getValue(PCodeType.PORT_LIN, foreignContractHead.getDestPort()));
            bizIEquipmentAgentAgreement.setExtend3("甲方：" + merchantMap.get(bizIEquipmentAgentAgreement.getCustomer()) +"（盖章）");
        }

        // 客商信息获取对应客户的档案信息
        BizMerchant merchant = new BizMerchant();
        merchant.setTradeCode(userInfo.getCompany());
        merchant.setMerchantCode(bizIEquipmentAgentAgreement.getCustomer());
        merchant.setCommonFlag("3");
        merchant.setMerchantType("0");
        List<BizMerchant> bizMerchants = bizMerchantMapper.select(merchant);
        if (CollectionUtils.isNotEmpty(bizMerchants)){
            BizMerchant bizMerchant = bizMerchants.get(0);
            bizIEquipmentAgentAgreement.setCustomerAddress(bizMerchant.getMerchantAddress());
            bizIEquipmentAgentAgreement.setLegalRepresentative(bizMerchant.getLegalPerson());
            bizIEquipmentAgentAgreement.setProjectContact(bizMerchant.getContactPerson());
            bizIEquipmentAgentAgreement.setContactWay(bizMerchant.getContactPhone());
            bizIEquipmentAgentAgreement.setCommunicationAddress(bizMerchant.getMerchantAddress());
            bizIEquipmentAgentAgreement.setAccountAddress(bizMerchant.getBankAddress());
            bizIEquipmentAgentAgreement.setOpeningBank(bizMerchant.getReceivingBank());
            bizIEquipmentAgentAgreement.setAccount(bizMerchant.getReceiverAccountNum());
            bizIEquipmentAgentAgreement.setTaxNo(bizMerchant.getTaxNo());
            bizIEquipmentAgentAgreement.setExpendAccount(merchant.getExpendAccount());
            bizIEquipmentAgentAgreement.setAddressAndPhone(bizMerchant.getMerchantAddress() + "  " + bizMerchant.getContactPhone());

        }

        bizIEquipmentAgentAgreement.setCustomer(merchantMap.get(bizIEquipmentAgentAgreement.getCustomer()));
        bizIEquipmentAgentAgreement.setSupplier(merchantMap.get(bizIEquipmentAgentAgreement.getSupplier()));
        bizIEquipmentAgentAgreement.setTotalQty(NumberFormatterUtils.formatNumber(totalQty));

        //签约地点转换
        City city = new City();
        city.setParamCode(bizIEquipmentAgentAgreement.getSignPlace());
        city.setTradeCode(userInfo.getCompany());
        List<City> cityList = cityMapper.select(city);
        if (CollectionUtils.isNotEmpty(cityList)) {
            bizIEquipmentAgentAgreement.setSignPlace(cityList.get(0).getCityCnName());
        }

        heads.add(bizIEquipmentAgentAgreement);

        String exportFileName = exportService.export(heads,lists, fileName, templateName);
        return exportFileName;
    }
}
