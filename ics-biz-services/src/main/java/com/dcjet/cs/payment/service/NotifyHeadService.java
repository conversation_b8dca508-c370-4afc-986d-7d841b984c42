package com.dcjet.cs.payment.service;



import com.dcjet.cs.baseInfoCustomerParams.dao.BaseInfoCustomerParamsMapper;
import com.dcjet.cs.baseInfoCustomerParams.model.BaseInfoCustomerParams;
import com.dcjet.cs.bi.dao.BiClientInformationMapper;
import com.dcjet.cs.bi.dao.BizMaterialInformationMapper;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BizMaterialInformation;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.dao.GwstdHttpConfigMapper;
import com.dcjet.cs.common.model.GwstdHttpConfig;
import com.dcjet.cs.dec.model.*;
import com.dcjet.cs.dto.payment.NotifyHeadDto;
import com.dcjet.cs.dto.payment.NotifyHeadParam;
import com.dcjet.cs.importedCigarettes.dao.BizIContractHeadMapper;
import com.dcjet.cs.payment.dao.BizPaymentSettlementMapper;
import com.dcjet.cs.payment.dao.NotifyHeadMapper;
import com.dcjet.cs.payment.dao.NotifyListMapper;
import com.dcjet.cs.payment.mapper.NotifyHeadDtoMapper;
import com.dcjet.cs.payment.model.BizPaymentSettlement;
import com.dcjet.cs.payment.model.NotifyHead;
import com.dcjet.cs.payment.model.NotifyList;
import com.dcjet.cs.service.ThirdPartyDbService;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.util.bulkSql.BulkSqlOpt;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import dm.jdbc.util.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.beans.Transient;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-3-11
 */
@Service
public class NotifyHeadService extends BaseService<NotifyHead> {
    @Resource
    private NotifyHeadMapper notifyHeadMapper;
    @Resource
    private BizIContractHeadMapper bizIContractHeadMapper;
    @Resource
    private BizPaymentSettlementMapper bizPaymentSettlementMapper;
    @Resource
    private NotifyHeadDtoMapper notifyHeadDtoMapper;
    @Resource
    private NotifyListMapper notifyListMapper;
    @Resource
    private BizMaterialInformationMapper bizMaterialInformationMapper;
    @Resource
    private BizMerchantMapper bizMerchantMapper;
    @Resource
    private BaseInfoCustomerParamsMapper baseInfoCustomerParamsMapper;
    @Resource
    private ThirdPartyDbService thirdPartyDbService;
    @Resource
    GwstdHttpConfigMapper gwstdHttpConfigMapper;
    @Override
    public Mapper<NotifyHead> getMapper() {
        return notifyHeadMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param notifyHeadParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<NotifyHeadDto>> getListPaged(NotifyHeadParam notifyHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        NotifyHead notifyHead = notifyHeadDtoMapper.toPo(notifyHeadParam);
        notifyHead.setTradeCode(userInfo.getCompany());
        Page<NotifyHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> notifyHeadMapper.getList(notifyHead));
        List<NotifyHeadDto> notifyHeadDtos = page.getResult().stream().map(head -> {
            NotifyHeadDto dto = notifyHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<NotifyHeadDto>> paged = ResultObject.createInstance(notifyHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param notifyHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public NotifyHeadDto insert(NotifyHeadParam notifyHeadParam, UserInfoToken userInfo) {
        NotifyHead notifyHead = notifyHeadDtoMapper.toPo(notifyHeadParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        notifyHead.setSid(sid);
        notifyHead.setInsertUser(userInfo.getUserNo());
        notifyHead.setInsertTime(new Date());
        notifyHead.setTradeCode(userInfo.getCompany());
        if (StringUtils.isNotBlank(notifyHead.getBizType())){
            if ("1".equals(notifyHead.getBizType())){
                notifyHead.setDocNo(getDocNo("JYFKTZ",userInfo));
            }else if ("2".equals(notifyHead.getBizType())){
                notifyHead.setDocNo(getDocNo("FLFKTZ",userInfo));
            }else if ("6".equals(notifyHead.getBizType())){
                notifyHead.setDocNo(getDocNo("JKFKTZ",userInfo));
            }else if ("3".equals(notifyHead.getBizType())){
                notifyHead.setDocNo(getDocNo("JYFKTZ",userInfo));
            }
        }
        // 新增数据
        int insertStatus = notifyHeadMapper.insert(notifyHead);
        return  insertStatus > 0 ? notifyHeadDtoMapper.toDto(notifyHead) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param notifyHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public NotifyHeadDto update(NotifyHeadParam notifyHeadParam, UserInfoToken userInfo) {
        NotifyHead notifyHead = notifyHeadMapper.selectByPrimaryKey(notifyHeadParam.getSid());

        String contractNo = notifyHead.getContractNo();
        String orderNumber = notifyHead.getOrderNumber();


        notifyHeadDtoMapper.updatePo(notifyHeadParam, notifyHead);
        notifyHead.setUpdateUser(userInfo.getUserNo());
        notifyHead.setUpdateTime(new Date());
        notifyHead.setTradeCode(userInfo.getCompany());
        notifyHead.setUpdateUserName(userInfo.getUserName());
        notifyHead.setContractNo(contractNo);
        notifyHead.setOrderNumber(orderNumber);
        // 更新数据
        int update = notifyHeadMapper.updateByPrimaryKey(notifyHead);
        //保存后更新表体
        List<NotifyList> select = notifyListMapper.select(new NotifyList() {{
            setHeadId(notifyHead.getSid());
        }});
        for (NotifyList notifyList : select) {
            notifyList.setPayAmtRmb(notifyList.getPayAmt().multiply(notifyHead.getRate()));
            notifyListMapper.updateByPrimaryKey(notifyList);
        }
        return update > 0 ? notifyHeadDtoMapper.toDto(notifyHead) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		notifyHeadMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<NotifyHeadDto> selectAll(NotifyHeadParam exportParam, UserInfoToken userInfo) {
        NotifyHead notifyHead = notifyHeadDtoMapper.toPo(exportParam);
        notifyHead.setTradeCode(userInfo.getCompany());
        List<NotifyHeadDto> notifyHeadDtos = new ArrayList<>();
        List<NotifyHead> notifyHeads = notifyHeadMapper.getList(notifyHead);
        if (CollectionUtils.isNotEmpty(notifyHeads)) {
            notifyHeadDtos = notifyHeads.stream().map(head -> {
                NotifyHeadDto dto = notifyHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return notifyHeadDtos;
    }

    public String getDocNo(String docNo,UserInfoToken userInfo) {
        // 1. 获取当前年月（格式：yyyyMM）
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        String currentYm = sdf.format(new Date());  // 示例输出：202310（2023年10月）

        // 2. 从数据库查询当前最大流水号（最后三位）
        String code = notifyHeadMapper.getDocNo(docNo+currentYm,userInfo.getCompany());

        // 3. 处理流水号逻辑
        if (StringUtils.isBlank(code)) {
            code = "001";  // 如果无记录，从001开始
        } else {
            int nextNumber = Integer.parseInt(code) + 1;
            if (nextNumber > 999) {
                nextNumber = 1;  // 超过999则重置为1（如：999 → 001）
            }
            code = String.format("%03d", nextNumber);  // 格式化为3位，不足补零
        }

        // 4. 拼接完整 doc_no
        return docNo + currentYm + code;  // 示例输出：JYFKTZ202310001
    }


    @Transactional(rollbackFor = Exception.class)
    public ResultObject confirmDataStatus(NotifyHeadParam notifyHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("确认成功"));
        NotifyHead notifyHead = notifyHeadMapper.selectByPrimaryKey(notifyHeadParam.getSid());
        if (notifyHead == null) {
            throw new ErrorException(400, "付款通知表头数据不存在，请刷新");
        }
        if ("1".equals(notifyHead.getDocStatus())){
            throw new ErrorException(400, "该数据已经确认，无需重复操作");
        }
        notifyHead.setDocStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        notifyHead.setUpdateUser(userInfo.getUserNo());
        notifyHead.setUpdateTime(new Date());
        notifyHead.setUpdateUserName(userInfo.getUserName());
        notifyHead.setTradeCode(userInfo.getCompany());
        notifyHead.setCfmTime(new Date());


        //流转货款结算
        String errorMsg = bizPaymentSettlementMapper.checkTransferData(notifyHead.getSid(),notifyHead.getBizType(),userInfo);
        if (StringUtils.isNotBlank(errorMsg)){
            throw new RuntimeException(errorMsg);
        }
        bizPaymentSettlementMapper.transferToPaymentSettlement(notifyHead.getSid(),notifyHead.getBizType(),userInfo);

        List<NotifyList> list = notifyListMapper.select(new NotifyList() {{
            setTradeCode(userInfo.getCompany());
            setHeadId(notifyHead.getSid());
        }});

        //todo 发送用友
        //用友----付款通知
        GwstdHttpConfig gwstdHttpConfig = gwstdHttpConfigMapper.selectByType("YonyouState");
        if(gwstdHttpConfig != null && "1".equals(gwstdHttpConfig.getServiceUrl())) {
            sendNotifyYonyou(notifyHead,list,userInfo);
        }
        notifyHeadMapper.updateByPrimaryKey(notifyHead);
        return result;
    }

    private void sendNotifyYonyou(NotifyHead head, List<NotifyList> list, UserInfoToken userInfo) {
        String id = UUID.randomUUID().toString().replace("-", StringUtils.EMPTY);
        OBillJck oBillJck = headMessageNotify(head, list, userInfo,id);
        List<OBillBJck> oBillBJcks = listMessageNotify(head, list, userInfo,id);
        //存储信息
        try {
            Map<String, Object> stringObjectMap = thirdPartyDbService.convertToMap(oBillJck);
            thirdPartyDbService.insertData("O_BILL_JCK",stringObjectMap);
            List<Map<String, Object>> maps = thirdPartyDbService.convertListToMapList(oBillBJcks);
            thirdPartyDbService.batchInsertData("O_BILL_B_JCK",maps);
        } catch (Exception e) {
            throw new ErrorException(500, "数据确认成功，发送用友失败！" + e);
        }
    }

    private List<OBillBJck> listMessageNotify(NotifyHead head, List<NotifyList> list, UserInfoToken userInfo,String id) {
        List<OBillBJck> oBillBJcks = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(list)){
            long[] seqNos = this.thirdPartyDbService.getSequenceNextValue("O_BILL_B_JCK_SEQ", list.size());
            for (int i = 0; i < list.size(); i++) {
                NotifyList notifyList = list.get(i);
                OBillBJck oBillBJck = new OBillBJck();
                //--字段名称---字段名-----------取值
                //关联主表外键  billid
                oBillBJck.setBillid(id);
                //存货编码	  inventory
                List<BizMaterialInformation> select = bizMaterialInformationMapper.select(new BizMaterialInformation() {{
                    setTradeCode(userInfo.getCompany());
                    setGname(notifyList.getGoodsName());
                }});
                if(CollectionUtils.isNotEmpty(select)){
                    oBillBJck.setInventory(select.get(0).getBarCode());
                }
                //数量	      tnumber
                oBillBJck.setTnumber(new BigDecimal(0));
                //计量单位	  mainunit
                List<BaseInfoCustomerParams> selectCurr = baseInfoCustomerParamsMapper.select(new BaseInfoCustomerParams() {{
                    setCustomParamCode(notifyList.getUnit());
                    setTradeCode(userInfo.getCompany());
                    setParamsType("UNIT");
                }});
                if(CollectionUtils.isNotEmpty(selectCurr)){
                    oBillBJck.setMainunit(selectCurr.get(0).getUnitYonyou());
                    oBillBJck.setNameMainunit(selectCurr.get(0).getParamsName());
                }
                //单价	      price
                oBillBJck.setPrice(new BigDecimal(0));
                //不含税金额	  notaxmoney
                oBillBJck.setNotaxmoney(notifyList.getPayAmtRmb());
                //金额	      totalmoney
                oBillBJck.setTotalmoney(notifyList.getPayAmtRmb());
                oBillBJck.setTaxmoney(new BigDecimal(0));

                //存货名称	  name_invmandoc
                oBillBJck.setNameInvmandoc(notifyList.getGoodsName());
                //系统时间	  ts
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String formattedDate = sdf.format(new Date());
                oBillBJck.setTs(formattedDate);
                //客商编码	  shipcustname
                oBillBJck.setShipcustname(head.getPayee());
                //发票号	      billno_bt
                oBillBJck.setBillnoBt(notifyList.getInvoiceNumber());
                //客商编码	  pk_shipcust
                if(StringUtil.isNotEmpty(head.getPayee())){
                    BizMerchant bizMerchant = new BizMerchant();
                    bizMerchant.setTradeCode(userInfo.getCompany());
                    bizMerchant.setMerchantCode(head.getPayee());
                    //查询出全部的客商信息
                    List<BizMerchant> select1 = bizMerchantMapper.select(bizMerchant);
                    if(CollectionUtils.isNotEmpty(select1)) {
                        oBillBJck.setPkShipcust(select1.get(0).getFinanceCode());
                    }
                }
                oBillBJck.setMqLsh((int) seqNos[i]);
                oBillBJck.setCurrentrate(new BigDecimal(1));
                oBillBJck.setBbje(notifyList.getPayAmtRmb());

                //收支项目编码 costsubj
                oBillBJck.setCostsubj("2001");
                //收支项目名称 name_costsubj
                oBillBJck.setNameCostsubj("货款");
                //           mq_op
                oBillBJck.setMqOp("i");
                //           mq_st
                oBillBJck.setMqSt("0");
                //           mq_count
                oBillBJck.setMqCount(1);
                //           pk_corp
                oBillBJck.setPkCorp("1022");
                oBillBJck.setDr(0);
                oBillBJck.setCurrentrate(new BigDecimal(1));
                oBillBJcks.add(oBillBJck);
            }
        }
        return oBillBJcks;
    }

    private OBillJck headMessageNotify(NotifyHead head, List<NotifyList> list, UserInfoToken userInfo,String id) {
        OBillJck oBillJck = new OBillJck();
//--------字段名称----字段名------------取值
        //单据主键　	billid
        oBillJck.setBillid(id);
        //单据编号　	billcode
        oBillJck.setBillcode(head.getDocNo());
        //单据日期	billdate
        if(head.getBizDate() != null){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String formattedDate = sdf.format(head.getBizDate());
            oBillJck.setBilldate(formattedDate);
        }
        //业务人员	person
        oBillJck.setPerson(head.getInsertUser());
        //制单人员	maker
        oBillJck.setMaker(head.getInsertUser());
        //客商编码	cust
        oBillJck.setCust(head.getPayee());
        //业务员名称	name_psndoc
        oBillJck.setNamePsndoc(head.getInsertUserName());
        //操作员名称	name_operator
        oBillJck.setNameOperator(head.getInsertUserName());
        //客商名称	name_cumandoc
        if(StringUtil.isNotEmpty(head.getPayee())){
            BizMerchant bizMerchant = new BizMerchant();
            bizMerchant.setTradeCode(userInfo.getCompany());
            bizMerchant.setMerchantCode(head.getPayee());
            //查询出全部的客商信息
            List<BizMerchant> select1 = bizMerchantMapper.select(bizMerchant);
            if(CollectionUtils.isNotEmpty(select1)){
                oBillJck.setNameCumandoc(select1.get(0).getMerchantNameCn());
            }
        }
        //系统时间	ts               接口数据发送时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedDate = sdf.format(new Date());
        oBillJck.setTs(formattedDate);

        if(CollectionUtils.isNotEmpty(list)) {
            oBillJck.setTotalmoney(head.getPayAmtRmb());
            //表体行数	row_count        接口表赋值：表体数据的行数
            oBillJck.setRowCount(list.size());
        }
        oBillJck.setMqLsh((int) this.thirdPartyDbService.getSequenceNextValue("O_BILL_JCK_SEQ", 1)[0]);
//--------字段名称-----字段名-----------默认值
        //单据类型	 rd_type           T3
        oBillJck.setRdType("A2");
        //库存组织	 rdcenter          JCK01
        oBillJck.setRdcenter("JCK01");
        //公司名称	 company           中国烟草上海进出口有限责任公司
        oBillJck.setCompany("中国烟草上海进出口有限责任公司");
        //部门编码	 deptdoc           04
        oBillJck.setDeptdoc("04");
        //库存组织名称 name_calbody      个别计价
        oBillJck.setNameCalbody("个别计价");
        //部门名称	 name_deptdoc      业务一部
        oBillJck.setNameDeptdoc("业务一部");
        //           mq_op             i
        oBillJck.setMqOp("1");
        //           mq_st             0
        oBillJck.setMqSt("0");
        //           mq_count          1
        oBillJck.setMqCount(1);
        //           pk_corp           1022
        oBillJck.setPkCorp("1022");
        oBillJck.setDr(0);
        //币种编码	 currtypecode      CNY
        oBillJck.setCurrtypecode("CNY");
        //币种名称	 currtypename      人民币
        oBillJck.setCurrtypename("人民币");
        oBillJck.setCurrentrate(new BigDecimal("1"));

        return oBillJck;
    }

    public ResultObject cancelDataStatus(NotifyHeadParam notifyHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("作废成功"));
        NotifyHead notifyHead = notifyHeadMapper.selectByPrimaryKey(notifyHeadParam.getSid());
        if (notifyHead == null) {
            throw new ErrorException(400, "付款通知表头数据不存在，请刷新");
        }
        //校验合同号是否在下游数据使用
        if (bizPaymentSettlementMapper.checkContractIsUsed(notifyHead.getContractNo()) > 0){
            throw new ErrorException(400, "货款结算数据存在此合同号,不允许作废");
        }
        notifyHead.setDocStatus(CommonEnum.OrderStatusEnum.CANCELLED.getValue());
        notifyHead.setUpdateUser(userInfo.getUserNo());
        notifyHead.setUpdateTime(new Date());
        notifyHead.setUpdateUserName(userInfo.getUserName());
        notifyHead.setTradeCode(userInfo.getCompany());
        notifyHead.setCfmTime(null);
        notifyHeadMapper.updateByPrimaryKey(notifyHead);

        return result;
    }

    public ResultObject backDataStatus(NotifyHeadParam notifyHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("退单成功"));
        NotifyHead notifyHead = notifyHeadMapper.selectByPrimaryKey(notifyHeadParam.getSid());
        if (notifyHead == null) {
            throw new ErrorException(400, "付款通知表头数据不存在，请刷新");
        }
        notifyHead.setDocStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        notifyHead.setUpdateUser(userInfo.getUserNo());
        notifyHead.setUpdateTime(new Date());
        notifyHead.setUpdateUserName(userInfo.getUserName());
        notifyHead.setTradeCode(userInfo.getCompany());
        notifyHead.setCfmTime(null);
        notifyHeadMapper.updateByPrimaryKey(notifyHead);
        return result;
    }

    public ResultObject copy(NotifyHeadParam notifyHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("复制成功"));
        NotifyHead notifyHead = notifyHeadMapper.selectByPrimaryKey(notifyHeadParam.getSid());
        if (notifyHead == null) {
            throw new ErrorException(400, "付款通知表头数据不存在，请刷新");
        }

        //表头
        NotifyHead newNotifyHead = new NotifyHead();
        BeanUtils.copyProperties(notifyHead, newNotifyHead);  // 复制所有属性
        String sid = UUID.randomUUID().toString();
        newNotifyHead.setDocStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        if (StringUtils.isNotBlank(newNotifyHead.getBizType())){
            if ("1".equals(newNotifyHead.getBizType())){
                newNotifyHead.setDocNo(getDocNo("JYFKTZ",userInfo));
            }else if ("2".equals(newNotifyHead.getBizType())){
                newNotifyHead.setDocNo(getDocNo("FLFKTZ",userInfo));
            }else if ("6".equals(newNotifyHead.getBizType())){
                newNotifyHead.setDocNo(getDocNo("JKFKTZ",userInfo));
            }
        }

        newNotifyHead.setSid(sid);
        newNotifyHead.setInsertTime(new Date());
        newNotifyHead.setBizDate(new Date());
        newNotifyHead.setUpdateUser(userInfo.getUserNo());
        newNotifyHead.setUpdateTime(new Date());
        newNotifyHead.setUpdateUserName(userInfo.getUserName());
        newNotifyHead.setInsertUser(userInfo.getUserNo());
        newNotifyHead.setTradeCode(userInfo.getCompany());
        newNotifyHead.setCfmTime(null);
        notifyHeadMapper.insert(newNotifyHead);


        //表体
        List<NotifyList>  notifyLists =  notifyListMapper.selectByHeadId(notifyHeadParam.getSid());
        List<NotifyList> insertNotifyList = new ArrayList<>();
        for (NotifyList notifyList : notifyLists) {
            NotifyList newNotifyList = new NotifyList();
            BeanUtils.copyProperties(notifyList, newNotifyList);  // 复制所有属性
            String sid2 = UUID.randomUUID().toString();
            newNotifyList.setSid(sid2);
            newNotifyList.setHeadId(sid);
            newNotifyList.setInsertTime(new Date());
            newNotifyList.setInsertUser(userInfo.getUserNo());
            insertNotifyList.add(newNotifyList);
        }
        if (CollectionUtils.isNotEmpty(insertNotifyList)) {
            BulkSqlOpt.batchInsertForErp(insertNotifyList, NotifyListMapper.class);
        }



        return result;
    }

    public ResultObject redFlushDataStatus(NotifyHeadParam notifyHeadParam, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("红冲成功"));
        NotifyHead notifyHead = notifyHeadMapper.selectByPrimaryKey(notifyHeadParam.getSid());
        if (notifyHead == null) {
            throw new ErrorException(400, "付款通知表头数据不存在，请刷新");
        }
        notifyHead.setRedFlush(CommonEnum.isNotEnum.type_1.getCode());
        notifyHead.setUpdateUser(userInfo.getUserNo());
        notifyHead.setUpdateTime(new Date());
        notifyHead.setUpdateUserName(userInfo.getUserName());
        notifyHead.setTradeCode(userInfo.getCompany());
        notifyHeadMapper.updateByPrimaryKey(notifyHead);
        return result;
    }
}
