package com.dcjet.cs.iEBusiness.service;
import com.dcjet.cs.Utils.NumberFormatterUtils;
import com.dcjet.cs.aeo.model.AeoAuditInfo;
import com.dcjet.cs.aeo.service.AeoAuditInfoService;
import com.dcjet.cs.approvalFlow.service.ApprovalFlowService;
import com.dcjet.cs.auxiliaryMaterials.model.BizINonStateAuxmatAggrContractList;
import com.dcjet.cs.bi.service.BizMerchantService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.common.model.WorkFlowParam;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.dto.aeo.ApprovalFlowParam;
import com.dcjet.cs.dto.iEBusiness.ForContractListDto;
import com.dcjet.cs.iEBusiness.dao.BizAgencyAgreementListMapper;
import com.dcjet.cs.iEBusiness.model.BizAgencyAgreementList;
import com.dcjet.cs.seven.dao.SevenForeignContractHeadMapper;
import com.dcjet.cs.seven.model.SevenForeignContractHead;
import com.dcjet.cs.util.CommonEnum;
import com.xdo.pcode.service.PCodeHolder;
import com.yuncheng.workflow.api.WorkFlowBatchApi;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.iEBusiness.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.iEBusiness.dao.BizAgencyAgreementMapper;
import com.dcjet.cs.iEBusiness.mapper.BizAgencyAgreementDtoMapper;
import com.dcjet.cs.iEBusiness.model.BizAgencyAgreement;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.yuncheng.workflow.model.vo.NextNodeInfoBatchVo;
import com.yuncheng.workflow.model.vo.NextNodeInfoVo;
import com.yuncheng.workflow.vo.HttpResult;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-7-7
 */
@Service
public class BizAgencyAgreementService extends BaseService<BizAgencyAgreement> implements ApprovalFlowService {
    @Resource
    private BizAgencyAgreementMapper bizAgencyAgreementMapper;
    @Resource
    private BizAgencyAgreementDtoMapper bizAgencyAgreementDtoMapper;
    @Resource
    private CommonService commonService;
    @Resource
    private AeoAuditInfoService aeoAuditInfoService;
    @Resource
    private SevenForeignContractHeadMapper sevenForeignContractHeadMapper;
    @Resource
    private BizAgencyAgreementListMapper bizAgencyAgreementListMapper;
    @Resource
    private ExportService exportService;
    @Resource
    private BizMerchantService bizMerchantService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Override
    public Mapper<BizAgencyAgreement> getMapper() {
        return bizAgencyAgreementMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizAgencyAgreementParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizAgencyAgreementDto>> getListPaged(BizAgencyAgreementParam bizAgencyAgreementParam, PageParam pageParam) {
        // 启用分页查询
        BizAgencyAgreement bizAgencyAgreement = bizAgencyAgreementDtoMapper.toPo(bizAgencyAgreementParam);
        Page<BizAgencyAgreement> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizAgencyAgreementMapper.getList(bizAgencyAgreement));
        List<BizAgencyAgreementDto> bizAgencyAgreementDtos = page.getResult().stream().map(head -> {
            BizAgencyAgreementDto dto = bizAgencyAgreementDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizAgencyAgreementDto>> paged = ResultObject.createInstance(bizAgencyAgreementDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }

    /**
     * 待审核页面查询
     * @param bizAgencyAgreementParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    public ResultObject<List<BizAgencyAgreementDto>> getAeoListPaged(BizAgencyAgreementParam bizAgencyAgreementParam, PageParam pageParam, UserInfoToken userInfo) {
        // 初始化 WorkflowBatchApi Service
        WorkFlowBatchApi workFlowBatchApi = commonService.buildWorkFlowBatchApi(userInfo);
        HttpResult result = workFlowBatchApi.queryApprovalList(bizAgencyAgreementParam.getBusinessType());
        List<String> ids = (List<String>) result.getResult();

        // 如果ids为空，直接返回空分页结果
        if (ids == null || ids.isEmpty()) {
            return ResultObject.createInstance(Collections.emptyList(), 0, pageParam.getPage());
        }

        bizAgencyAgreementParam.setIds(ids);

        // 启用分页查询
        BizAgencyAgreement bizAgencyAgreement = bizAgencyAgreementDtoMapper.toPo(bizAgencyAgreementParam);
        bizAgencyAgreement.setTradeCode(userInfo.getCompany());
        Page<BizAgencyAgreement> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizAgencyAgreementMapper.getAeoList(bizAgencyAgreement));
        // 将PO转为DTO返回给前端
        List<BizAgencyAgreementDto> bizAgencyAgreementDtoList = page.getResult().stream()
                .map(bizAgencyAgreementDtoMapper::toDto)
                .collect(Collectors.toList());

        return ResultObject.createInstance(bizAgencyAgreementDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 功能描述:新增
     *
     * @param bizAgencyAgreementParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizAgencyAgreementDto insert(BizAgencyAgreementParam bizAgencyAgreementParam, UserInfoToken userInfo) {
        BizAgencyAgreement bizAgencyAgreement = bizAgencyAgreementDtoMapper.toPo(bizAgencyAgreementParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizAgencyAgreement.setId(sid);
        bizAgencyAgreement.setCreateBy(userInfo.getUserNo());
        bizAgencyAgreement.setCreateTime(new Date());
        bizAgencyAgreement.setMaker(userInfo.getUserName());
        bizAgencyAgreement.setMakeDate(new Date());
        bizAgencyAgreement.setBillStatus(CommonEnum.OrderStatusEnum.DRAFT.getValue());
        bizAgencyAgreement.setBusinessType(CommonEnum.businessTypeEnum.type_7.getCode());
        bizAgencyAgreement.setApprovalStatus(CommonEnum.APPR_STAUTS_ENUM.APPR_STAUTS_0.getCode());
        bizAgencyAgreement.setTradeCode(userInfo.getCompany());
        bizAgencyAgreement.setParentId(bizAgencyAgreementParam.getId());

        bizAgencyAgreementMapper.insertListByForContract(bizAgencyAgreementParam.getId(), sid, userInfo.getUserNo());
        BigDecimal total = bizAgencyAgreementMapper.getContractAmount(sid);

        // 根据外商合同表头信息，带入协议信息
        SevenForeignContractHead sevenForeignContractHead = sevenForeignContractHeadMapper.selectByPrimaryKey(bizAgencyAgreementParam.getId());
        bizAgencyAgreement.setContractNo(sevenForeignContractHead.getContractNo());
        bizAgencyAgreement.setCustomer(sevenForeignContractHead.getSeller());
        bizAgencyAgreement.setSigningDate(sevenForeignContractHead.getSignDate());
        bizAgencyAgreement.setSigningPlace(sevenForeignContractHead.getSignPlaceCn());
        bizAgencyAgreement.setCurrency(sevenForeignContractHead.getCurr());
        bizAgencyAgreement.setContractAmount(total);

        //新增审核记录
        AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
        aeoAuditInfo.setBusinessSid(bizAgencyAgreement.getId());
        aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), "0", userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());

        // 新增数据
        int insertStatus = bizAgencyAgreementMapper.insert(bizAgencyAgreement);
        return  insertStatus > 0 ? bizAgencyAgreementDtoMapper.toDto(bizAgencyAgreement) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizAgencyAgreementParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizAgencyAgreementDto update(BizAgencyAgreementParam bizAgencyAgreementParam, UserInfoToken userInfo) {
        //校验协议编号唯一性
        if (bizAgencyAgreementMapper.checkAgreementNo(bizAgencyAgreementParam.getAgreementNo(),bizAgencyAgreementParam.getId()) > 0){
            throw new ErrorException(400, "协议编号已存在，请重新输入！");
        }
        BizAgencyAgreement bizAgencyAgreement = bizAgencyAgreementMapper.selectByPrimaryKey(bizAgencyAgreementParam.getId());
        bizAgencyAgreementDtoMapper.updatePo(bizAgencyAgreementParam, bizAgencyAgreement);
        bizAgencyAgreement.setUpdateBy(userInfo.getUserNo());
        bizAgencyAgreement.setUpdateTime(new Date());
        bizAgencyAgreement.setMaker(userInfo.getUserName());
        bizAgencyAgreement.setMakeDate(new Date());
        // 更新数据
        int update = bizAgencyAgreementMapper.updateByPrimaryKey(bizAgencyAgreement);
        return update > 0 ? bizAgencyAgreementDtoMapper.toDto(bizAgencyAgreement) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
        if (bizAgencyAgreementMapper.checkCanDelBySids(sids) > 0){
            throw new ErrorException(400, "仅编制状态数据允许删除");
        }
		bizAgencyAgreementMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizAgencyAgreementDto> selectAll(BizAgencyAgreementParam exportParam, UserInfoToken userInfo) {
        BizAgencyAgreement bizAgencyAgreement = bizAgencyAgreementDtoMapper.toPo(exportParam);
        // bizAgencyAgreement.setTradeCode(userInfo.getCompany());
        List<BizAgencyAgreementDto> bizAgencyAgreementDtos = new ArrayList<>();
        List<BizAgencyAgreement> bizAgencyAgreements = bizAgencyAgreementMapper.getList(bizAgencyAgreement);
        if (CollectionUtils.isNotEmpty(bizAgencyAgreements)) {
            bizAgencyAgreementDtos = bizAgencyAgreements.stream().map(head -> {
                BizAgencyAgreementDto dto = bizAgencyAgreementDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizAgencyAgreementDtos;
    }

    public ResultObject confirmStatus(BizAgencyAgreementParam param, UserInfoToken userInfo) {
        ResultObject result = ResultObject.createInstance(true, "确认成功");
        List<String> sids = param.getIds();
        if (CollectionUtils.isEmpty(sids)) {
            throw new ErrorException(400, "请选择要确认的数据！");
        }
        for (String sid : sids) {
            BizAgencyAgreement bizAgencyAgreement = bizAgencyAgreementMapper.selectByPrimaryKey(sid);
            if (bizAgencyAgreement == null) {
                throw new ErrorException(400, "代理协议数据不存在，请刷新");
            }
            if (CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizAgencyAgreement.getBillStatus())) {
                throw new ErrorException(400, "该数据已经确认，无需重复操作");
            }
            bizAgencyAgreement.setBillStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
            bizAgencyAgreement.setConfirmTime(new Date());
            bizAgencyAgreement.setUpdateBy(userInfo.getUserNo());
            bizAgencyAgreement.setUpdateTime(new Date());
            bizAgencyAgreementMapper.updateByPrimaryKey(bizAgencyAgreement);
        }
        return result;
    }

    /**
     * 获取外商合同列表（用于代理协议选择合同）
     * @param userInfo
     * @return
     */
    public List<ForContractListDto> getForContractList(BizAgencyAgreementParam param, UserInfoToken userInfo) {
        return bizAgencyAgreementMapper.getForContractList(userInfo.getCompany(), param.getContractNo());
    }

    @Override
    public void startFlowBatch(NextNodeInfoBatchVo batchVo, ApprovalFlowParam approvalFlowParam, UserInfoToken userInfo) {
        //更新表头状态
        BizAgencyAgreement bizAgencyAgreement = bizAgencyAgreementMapper.selectByPrimaryKey(approvalFlowParam.getIds().get(0));
        if (!CommonEnum.OrderApprStatusEnum.NOT_APPROVED.getValue().equals(bizAgencyAgreement.getApprovalStatus())
                && !CommonEnum.OrderApprStatusEnum.APPROVAL_REJECTED.getValue().equals(bizAgencyAgreement.getApprovalStatus())){
            throw new ErrorException(400, "只有未审核/审核退回数据允许操作发送审批");
        }
        bizAgencyAgreement.setApprovalStatus(CommonEnum.OrderApprStatusEnum.APPROVING.getValue());
        bizAgencyAgreement.setUpdateBy(userInfo.getUserNo());
        bizAgencyAgreement.setUpdateTime(new Date());
        //记录flowInstanceId
        bizAgencyAgreement.setExtend2(batchVo.getFlowInstanceId().get(0).get("flowInstanceId"));
        bizAgencyAgreementMapper.updateByPrimaryKey(bizAgencyAgreement);

        //新增审核记录
        AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
        aeoAuditInfo.setBusinessSid(bizAgencyAgreement.getId());
        aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), "发送审核", userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
    }

    @Override
    public void audit(List<NextNodeInfoVo> nextNodeInfoVos, ApprovalFlowParam approvalFlowParam, Map<String, String> flowInstanceMap, UserInfoToken userInfo) {
        for (NextNodeInfoVo nextNodeInfoVo : nextNodeInfoVos) {
            BizAgencyAgreement bizAgencyAgreement = bizAgencyAgreementMapper.selectByPrimaryKey(flowInstanceMap.get(nextNodeInfoVo.getFlowInstanceId()));
            if (nextNodeInfoVo.isFinish()) {
                bizAgencyAgreement.setApprovalStatus(CommonEnum.OrderApprStatusEnum.APPROVED.getValue());
                bizAgencyAgreement.setUpdateBy(userInfo.getUserNo());
                bizAgencyAgreement.setUpdateTime(new Date());
                bizAgencyAgreementMapper.updateByPrimaryKey(bizAgencyAgreement);
                nextNodeInfoVo.setNodeName("审核通过");
            }
            //新增审核记录
            AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
            aeoAuditInfo.setBusinessSid(bizAgencyAgreement.getId());
            aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), nextNodeInfoVo.getNodeName(), userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
        }
    }

    @Override
    public void reject(List<NextNodeInfoVo> nextNodeInfoVos, ApprovalFlowParam approvalFlowParam, UserInfoToken userInfo) {
        for (NextNodeInfoVo nextNodeInfoVo : nextNodeInfoVos) {
            //仅单条退回
            //回退到发起人
            BizAgencyAgreement bizAgencyAgreement = bizAgencyAgreementMapper.selectByPrimaryKey(approvalFlowParam.getIds().get(0));

            bizAgencyAgreement.setApprovalStatus(CommonEnum.OrderApprStatusEnum.APPROVAL_REJECTED.getValue());
            bizAgencyAgreement.setUpdateBy(userInfo.getUserNo());
            bizAgencyAgreement.setUpdateTime(new Date());
            bizAgencyAgreementMapper.updateByPrimaryKey(bizAgencyAgreement);

            //新增审核记录
            AeoAuditInfo aeoAuditInfo = new AeoAuditInfo();
            aeoAuditInfo.setBusinessSid(bizAgencyAgreement.getId());
            aeoAuditInfo.setApprNote(approvalFlowParam.getApprMessage());
            aeoAuditInfoService.auditInsert(aeoAuditInfo, new Date(), "审核退回", userInfo.getUserNo(), userInfo.getUserName(), userInfo.getCompany());
        }
    }

    @Override
    public List<WorkFlowParam> getFlowList(List<String> ids) {
        return bizAgencyAgreementMapper.getFlowList(ids);
    }

    public String print(String templateName, BizAgencyAgreementParam param, UserInfoToken userInfo) {
        templateName = templateName + ".xlsx";
        String fileName = UUID.randomUUID().toString() + ".xlsx";
        List<BizAgencyAgreement> heads = new ArrayList<>();
        List<BizAgencyAgreementList> lists = new ArrayList<>();
        if (StringUtils.isEmpty(param.getId())){
            throw new ErrorException(400, "代理协议表头不能为空");
        }
        BizAgencyAgreement bizAgencyAgreement = bizAgencyAgreementMapper.selectByPrimaryKey(param.getId());

        SevenForeignContractHead sevenForeignContractHead = sevenForeignContractHeadMapper.selectByPrimaryKey(bizAgencyAgreement.getParentId());

        String priceTerm = sevenForeignContractHead.getPriceTerm() + " " + pCodeHolder.getValue(PCodeType.PORT_LIN, sevenForeignContractHead.getDestPort());

        BizAgencyAgreementList listParam=new BizAgencyAgreementList();
        listParam.setHeadId(param.getId());
        listParam.setIEMark("E");
        lists = bizAgencyAgreementListMapper.select(listParam);

        if (CollectionUtils.isEmpty(lists)){
            throw new ErrorException(400, "表体数据不能为空");
        }
        BigDecimal totalQty = lists.stream().filter(Objects::nonNull).map(BizAgencyAgreementList::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
        //数量除以1000
        totalQty = totalQty.divide(BigDecimal.valueOf(1000), 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal totalAmount = lists.stream().filter(Objects::nonNull).map(BizAgencyAgreementList::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        bizAgencyAgreement.setExtend1("兹就甲方委托乙方代理出口 "+ lists.get(0).getGoodsName() +" 的有关事项达成下列协议：");
        //表头数据处理
        Map<String, String> merchantMap = bizMerchantService.getMerchantMap(userInfo);
        bizAgencyAgreement.setCustomer(merchantMap.get(bizAgencyAgreement.getCustomer()));
        bizAgencyAgreement.setSumStr("合计（" + priceTerm + "）");
        bizAgencyAgreement.setQtyStr(NumberFormatterUtils.formatNumber(totalQty) + "吨");
        bizAgencyAgreement.setTotalAmountStr(NumberFormatterUtils.formatNumber(totalAmount) + bizAgencyAgreement.getCurrency());

        heads.add(bizAgencyAgreement);

        String exportFileName = exportService.export(heads,lists, fileName, templateName);

        return exportFileName;
    }

    public String sign(String templateName, BizAgencyAgreementParam param, UserInfoToken userInfo) {
        templateName = templateName + ".xlsx";
        String fileName = UUID.randomUUID().toString() + ".xlsx";
        List<BizAgencyAgreement> heads = new ArrayList<>();
        List<BizAgencyAgreementList> lists = new ArrayList<>();
        if (StringUtils.isEmpty(param.getId())){
            throw new ErrorException(400, "代理协议表头不能为空");
        }
        BizAgencyAgreement bizAgencyAgreement = bizAgencyAgreementMapper.selectByPrimaryKey(param.getId());

        SevenForeignContractHead sevenForeignContractHead = sevenForeignContractHeadMapper.selectByPrimaryKey(bizAgencyAgreement.getParentId());

        String priceTerm = sevenForeignContractHead.getPriceTerm() + " " + pCodeHolder.getValue(PCodeType.PORT_LIN, sevenForeignContractHead.getDestPort());

        BizAgencyAgreementList listParam=new BizAgencyAgreementList();
        listParam.setHeadId(param.getId());
        listParam.setIEMark("E");
        lists = bizAgencyAgreementListMapper.select(listParam);

        if (CollectionUtils.isEmpty(lists)){
            throw new ErrorException(400, "表体数据不能为空");
        }
        BigDecimal totalQty = lists.stream().filter(Objects::nonNull).map(BizAgencyAgreementList::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
        //数量除以1000
        totalQty = totalQty.divide(BigDecimal.valueOf(1000), 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal totalAmount = lists.stream().filter(Objects::nonNull).map(BizAgencyAgreementList::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        //表头数据处理
        Map<String, String> merchantMap = bizMerchantService.getMerchantMap(userInfo);
        bizAgencyAgreement.setCustomer(merchantMap.get(bizAgencyAgreement.getCustomer()));
        bizAgencyAgreement.setQtyStr(NumberFormatterUtils.formatNumber(totalQty) + "吨");


        //金额
        bizAgencyAgreement.setTotalAmountStr("(小写)：" + bizAgencyAgreement.getCurrency() + NumberFormatterUtils.formatNumber(totalAmount));
        bizAgencyAgreement.setTotalAmountUpStr("(大写)：" + commonService.getCurrCn(bizAgencyAgreement.getCurrency(), PCodeType.CURR_ALL) + NumberFormatterUtils.convertToChineseAmount(totalAmount));

        bizAgencyAgreement.setExtend1("我司代理上海烟草集团有限责任公司向" + bizAgencyAgreement.getCustomer() +"出口"+ lists.get(0).getGoodsName() + " " +
                bizAgencyAgreement.getQtyStr() +"。合同总价为"+ bizAgencyAgreement.getCurrency() + NumberFormatterUtils.formatNumber(totalAmount) + getSaleString(bizAgencyAgreement.getAgreementTerms()) + "产品的技术规格、付款方式以及交货期均经过合同双方确认，我司收取 "+
                bizAgencyAgreement.getAgencyRate() +"%的代理费。");

        heads.add(bizAgencyAgreement);

        String exportFileName = exportService.export(heads,lists, fileName, templateName);

        return exportFileName;
    }

    public String getSaleString(String str){
        if (StringUtils.isBlank(str)){
            return "";
        }
        return str;
    }
}
