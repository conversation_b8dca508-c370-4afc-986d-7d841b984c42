<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.auxiliaryMaterials.dao.OrderNoticeListMapper">
    <resultMap id="orderNoticeListResultMap" type="com.dcjet.cs.auxiliaryMaterials.model.OrderNoticeList">
        <id column="ID" property="id" jdbcType="VARCHAR"/>
        <result column="HEAD_ID" property="headId" jdbcType="VARCHAR"/>
        <result column="PARENT_ID" property="parentId" jdbcType="VARCHAR"/>
        <result column="PREV_VERSION_ID" property="prevVersionId" jdbcType="VARCHAR"/>
        <result column="BUY_LIST_ID" property="buyListId" jdbcType="VARCHAR"/>
        <result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR"/>
        <result column="PRODUCT_MODEL" property="productModel" jdbcType="VARCHAR"/>
        <result column="SPECIFICATION" property="specification" jdbcType="VARCHAR"/>
        <result column="WEIGHT" property="weight" jdbcType="NUMERIC"/>
        <result column="SUPPLIER" property="supplier" jdbcType="VARCHAR"/>
        <result column="SUPPLIER_NAME" property="supplierName" jdbcType="VARCHAR"/>
        <result column="TRANSPORT_MODE" property="transportMode" jdbcType="VARCHAR"/>
        <result column="PORT" property="port" jdbcType="VARCHAR"/>
        <result column="QTY" property="qty" jdbcType="NUMERIC"/>
        <result column="UNIT" property="unit" jdbcType="VARCHAR"/>
        <result column="UNIT_PRICE" property="unitPrice" jdbcType="VARCHAR"/>
        <result column="REQUEST_DELIVERY_DATE" property="requestDeliveryDate" jdbcType="TIMESTAMP"/>
        <result column="DATA_STATUS" property="dataStatus" jdbcType="VARCHAR"/>
        <result column="PURCHASE_SALES_CONTRACT_NO" property="purchaseSalesContractNo" jdbcType="VARCHAR"/>
        <result column="NOTE" property="note" jdbcType="VARCHAR"/>
        <result column="CONFIRM_TIME" property="confirmTime" jdbcType="TIMESTAMP"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="SYS_ORG_CODE" property="sysOrgCode" jdbcType="VARCHAR"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="CREATE_BY_NAME" property="createByName" jdbcType="VARCHAR"/>
        <result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_BY_NAME" property="updateByName" jdbcType="VARCHAR"/>
        <result column="EXTEND1" property="extend1" jdbcType="VARCHAR"/>
        <result column="EXTEND2" property="extend2" jdbcType="VARCHAR"/>
        <result column="EXTEND3" property="extend3" jdbcType="VARCHAR"/>
        <result column="EXTEND4" property="extend4" jdbcType="VARCHAR"/>
        <result column="EXTEND5" property="extend5" jdbcType="VARCHAR"/>
        <result column="EXTEND6" property="extend6" jdbcType="VARCHAR"/>
        <result column="EXTEND7" property="extend7" jdbcType="VARCHAR"/>
        <result column="EXTEND8" property="extend8" jdbcType="VARCHAR"/>
        <result column="EXTEND9" property="extend9" jdbcType="VARCHAR"/>
        <result column="EXTEND10" property="extend10" jdbcType="VARCHAR"/>
        <result column="MEASURE_UNIT" property="measureUnit" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="columns">
        ID,
        PARENT_ID,
        HEAD_ID,
        PREV_VERSION_ID,
        BUY_LIST_ID,
        PRODUCT_NAME,
        PRODUCT_MODEL,
        SPECIFICATION,
        WEIGHT,
        SUPPLIER,
        SUPPLIER_NAME,
        TRANSPORT_MODE,
        PORT,
        QTY,
        UNIT,
        UNIT_PRICE,
        REQUEST_DELIVERY_DATE,
        DATA_STATUS,
        PURCHASE_SALES_CONTRACT_NO,
        NOTE,
        CONFIRM_TIME,
        TRADE_CODE,
        SYS_ORG_CODE,
        CREATE_BY,
        CREATE_TIME,
        CREATE_BY_NAME,
        UPDATE_BY,
        UPDATE_TIME,
        UPDATE_BY_NAME,
        EXTEND1,
        EXTEND2,
        EXTEND3,
        EXTEND4,
        EXTEND5,
        EXTEND6,
        EXTEND7,
        EXTEND8,
        EXTEND9,
        EXTEND10
    </sql>

    <sql id="condition">
        <if test="true">
            TRADE_CODE = #{tradeCode}
        </if>
        <if test="headId != null and headId != ''">
            and head_id = #{headId}
        </if>
        <if test="productName != null and productName != ''">
            and PRODUCT_NAME = #{productName}
        </if>
        <if test="orderNo != null and orderNo != ''">
            and HEAD_ID in (select ID from T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD where ORDER_NO = #{orderNo} and TRADE_CODE = #{tradeCode} and DATA_STATUS != '2')
        </if>
    </sql>
    <update id="updateCorrelationID">
        update T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST set
            buy_list_id = #{newListId} where buy_list_id = #{oldSid};
    </update>

    <select id="getList" resultMap="orderNoticeListResultMap"
            parameterType="com.dcjet.cs.auxiliaryMaterials.model.OrderNoticeList">
        select
        <include refid="columns"/>
        from T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST
        <where>
            <include refid="condition"/>
        </where>
        order by CREATE_TIME desc
    </select>

    <select id="getTotalQty" resultType="java.math.BigDecimal">
        select SUM(QTY)
        from T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST
        <where>
            <include refid="condition"/>
        </where>
    </select>

    <select id="getByHeadIds" resultMap="orderNoticeListResultMap">
        select
        <include refid="columns"/>
        from T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST
        where TRADE_CODE = #{tradeCode} and HEAD_ID in
        <foreach collection="headIds" item="headId" open="(" separator="," close=")">
            #{headId}
        </foreach>
        order by CREATE_TIME desc
    </select>

    <delete id="deleteByIds" parameterType="java.util.List">
        delete from T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST where ID in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByHeadIds" parameterType="java.util.List">
        delete from T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST
        where TRADE_CODE = #{tradeCode} and HEAD_ID in
        <foreach collection="headIds" item="headId" open="(" separator="," close=")">
            #{headId}
        </foreach>
    </delete>

    <select id="getCountByBuyListIds" resultType="java.lang.Integer">
        select count(1) from T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST
        where TRADE_CODE = #{tradeCode} and DATA_STATUS &lt;> '2' and BUY_LIST_ID in
        <foreach collection="buyListIds" item="buyListId" open="(" separator="," close=")">
            #{buyListId}
        </foreach>
    </select>

    <select id="getGeQtyCountByListIds" resultType="java.lang.Integer">
        select
            count(1)
        from
            (select
                 SUM(QTY) TOTAL_QTY,
                 BUY_LIST_ID
             from
                 T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST
             where
                 TRADE_CODE = #{tradeCode}
                 and DATA_STATUS &lt;> '2'
                 and BUY_LIST_ID in
                    <foreach collection="buyListIds" item="buyListId" open="(" separator="," close=")">
                        #{buyListId}
                    </foreach>
             group by
                 BUY_LIST_ID) NOTICE_LIST
            left join T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST BUY_LIST on NOTICE_LIST.BUY_LIST_ID = BUY_LIST.ID
        where
            NOTICE_LIST.TOTAL_QTY - BUY_LIST.QTY >= 0
    </select>

    <update id="confirmByHeadId">
        update T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST
        set DATA_STATUS    = '1',
            CONFIRM_TIME   = current_timestamp,
            UPDATE_BY      = #{updateBy},
            UPDATE_BY_NAME = #{updateByName},
            UPDATE_TIME    = current_timestamp
        where HEAD_ID = #{headId}
    </update>

    <update id="invalidateByHeadId">
        update T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST
        set DATA_STATUS    = '2',
            CONFIRM_TIME   = null,
            UPDATE_BY      = #{updateBy},
            UPDATE_BY_NAME = #{updateByName},
            UPDATE_TIME    = current_timestamp
        where HEAD_ID = #{headId}
    </update>

    <select id="getRemainUnSelectedData" resultMap="orderNoticeListResultMap">
        with TEMP_UNIT_PARAMS as (select * from (
        select PARAMS_CODE, PARAMS_NAME, ROW_NUMBER() over (partition by PARAMS_CODE ORDER BY INSERT_TIME) RN
            from T_BIZ_CUSTOMS_PARAMS where UPPER(PARAMS_TYPE) = 'UNIT' and TRADE_CODE = #{tradeCode})
        where RN = 1)
        select
            BUY_LIST.ID,
            BUY_LIST.HEAD_ID,
            BUY_LIST.ID as             BUY_LIST_ID,
            BUY_LIST.G_NAME as         PRODUCT_NAME,
            BUY_LIST.G_MODEL as        PRODUCT_MODEL,
            BUY_LIST.SPECIFICATIONS as SPECIFICATION,
            BUY_LIST.GRAM_WEIGHT as    WEIGHT,
            BUY_LIST.SUPPLIER,
            BUY_LIST.QTY,
            (case when IMPORT_UNIT_PARAMS.PARAMS_NAME is not null then CONCAT(BUY_LIST.UNIT, ' ', IMPORT_UNIT_PARAMS.PARAMS_NAME)
                else BUY_LIST.UNIT end) as UNIT,
            (case when MEASURE_UNIT_PARAMS.PARAMS_NAME is not null then CONCAT(QUOTATION.UNIT, ' ', MEASURE_UNIT_PARAMS.PARAMS_NAME)
                else QUOTATION.UNIT end) as MEASURE_UNIT
        from
            (select
                 *
             from
                 T_BIZ_I_AUXMAT_BUY_CONTRACT_LIST
             where
                 HEAD_ID in
                <foreach collection="contractIds" item="contractId" open="(" separator="," close=")">
                    #{contractId}
                </foreach>
                 and TRADE_CODE = #{tradeCode}) BUY_LIST
            left join (select
                           BUY_LIST_ID,
                           TRADE_CODE,
                           SUM(QTY) as TOTAL_QTY
                       from
                           T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST
                       where
                           DATA_STATUS &lt;> '2'
                       group by
                           TRADE_CODE, BUY_LIST_ID) NOTICE_LIST on BUY_LIST.ID = NOTICE_LIST.BUY_LIST_ID and BUY_LIST.TRADE_CODE = NOTICE_LIST.TRADE_CODE
            left join (select
                           *
                       from
                           (select
                                UNIT,
                                PRODUCT_MODEL,
                                G_NAME,
                                SPECIFICATIONS,
                                ROW_NUMBER() over(partition by G_NAME, SPECIFICATIONS ORDER BY CREATE_TIME) as RN
                            from
                                T_BIZ_QUOTATION
                            where
                                TRADE_CODE = #{tradeCode}) Q
                       where
                           RN = 1) QUOTATION on BUY_LIST.G_NAME = QUOTATION.G_NAME and BUY_LIST.SPECIFICATIONS = QUOTATION.SPECIFICATIONS
            left join TEMP_UNIT_PARAMS MEASURE_UNIT_PARAMS on QUOTATION.UNIT = MEASURE_UNIT_PARAMS.PARAMS_CODE
            left join TEMP_UNIT_PARAMS IMPORT_UNIT_PARAMS on BUY_LIST.UNIT = IMPORT_UNIT_PARAMS.PARAMS_CODE
        where BUY_LIST.QTY - COALESCE(NOTICE_LIST.TOTAL_QTY, 0) > 0
    </select>

    <update id="invalidateByOrderNo">
        update T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST
        set DATA_STATUS    = '2',
            CONFIRM_TIME   = null,
            UPDATE_BY      = #{updateBy},
            UPDATE_BY_NAME = #{updateByName},
            UPDATE_TIME    = current_timestamp
        where DATA_STATUS &lt;> '2'
          and TRADE_CODE = #{tradeCode}
          and HEAD_ID in
              (select ID
               from T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD
               where TRADE_CODE = #{tradeCode}
                 and ORDER_NO = #{orderNo})
    </update>
</mapper>